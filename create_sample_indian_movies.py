#!/usr/bin/env python3
"""
Create a sample Indian movie dataset for testing the MUSE framework adaptation.
This includes popular Bollywood and regional Indian movies.
"""

import json
import random
from typing import Dict, List

def create_sample_indian_movies() -> Dict:
    """Create a sample dataset of Indian movies in MUSE format"""
    
    sample_movies = [
        {
            "movie_id": "1001",
            "title": "3 Idiots",
            "original_title": "3 Idiots",
            "genres": ["Comedy", "Drama"],
            "description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots.",
            "year": "2009",
            "director": "<PERSON><PERSON>",
            "cast": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
            "language": "hi",
            "rating": 8.4,
            "cultural_themes": ["Education", "Friendship", "Engineering", "Bollywood"]
        },
        {
            "movie_id": "1002", 
            "title": "Dangal",
            "original_title": "Dangal",
            "genres": ["Biography", "Drama", "Sport"],
            "description": "Former wrestler <PERSON><PERSON><PERSON> and his two wrestler daughters struggle towards glory at the Commonwealth Games in the face of societal oppression.",
            "year": "2016",
            "director": "<PERSON>tesh <PERSON>i<PERSON>",
            "cast": ["Aamir <PERSON>", "Fatima Sana Shaikh", "<PERSON>ya <PERSON>hotra"],
            "language": "hi",
            "rating": 8.3,
            "cultural_themes": ["Wrestling", "Women Empowerment", "Sports", "Family"]
        },
        {
            "movie_id": "1003",
            "title": "Baahubali: The Beginning",
            "original_title": "Baahubali: The Beginning",
            "genres": ["Action", "Drama", "Fantasy"],
            "description": "In ancient India, an adventurous and daring man becomes involved in a decades old feud between two warring people.",
            "year": "2015",
            "director": "S.S. Rajamouli",
            "cast": ["Prabhas", "Rana Daggubati", "Anushka Shetty", "Tamannaah"],
            "language": "te",
            "rating": 8.0,
            "cultural_themes": ["Epic", "Kingdom", "War", "Telugu Cinema"]
        },
        {
            "movie_id": "1004",
            "title": "Zindagi Na Milegi Dobara",
            "original_title": "Zindagi Na Milegi Dobara",
            "genres": ["Adventure", "Comedy", "Drama"],
            "description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged.",
            "year": "2011",
            "director": "Zoya Akhtar",
            "cast": ["Hrithik Roshan", "Farhan Akhtar", "Abhay Deol", "Katrina Kaif"],
            "language": "hi",
            "rating": 8.2,
            "cultural_themes": ["Friendship", "Travel", "Adventure", "Modern India"]
        },
        {
            "movie_id": "1005",
            "title": "Lagaan",
            "original_title": "Lagaan: Once Upon a Time in India",
            "genres": ["Adventure", "Drama", "Musical"],
            "description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers.",
            "year": "2001",
            "director": "Ashutosh Gowariker",
            "cast": ["Aamir Khan", "Gracy Singh", "Rachel Shelley"],
            "language": "hi",
            "rating": 8.1,
            "cultural_themes": ["Cricket", "British Raj", "Village Life", "Patriotism"]
        },
        {
            "movie_id": "1006",
            "title": "Queen",
            "original_title": "Queen",
            "genres": ["Comedy", "Drama"],
            "description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled.",
            "year": "2013",
            "director": "Vikas Bahl",
            "cast": ["Kangana Ranaut", "Rajkummar Rao", "Lisa Haydon"],
            "language": "hi",
            "rating": 8.2,
            "cultural_themes": ["Women Empowerment", "Solo Travel", "Self Discovery"]
        },
        {
            "movie_id": "1007",
            "title": "Tumhari Sulu",
            "original_title": "Tumhari Sulu",
            "genres": ["Comedy", "Drama", "Family"],
            "description": "A happy-go-lucky Mumbai suburban housewife Sulochana, fondly known as Sulu, lands the role of a night RJ, resulting in drastic changes to her routine life.",
            "year": "2017",
            "director": "Suresh Triveni",
            "cast": ["Vidya Balan", "Manav Kaul", "Neha Dhupia"],
            "language": "hi",
            "rating": 7.1,
            "cultural_themes": ["Middle Class", "Radio", "Mumbai", "Family"]
        },
        {
            "movie_id": "1008",
            "title": "Andhadhun",
            "original_title": "Andhadhun",
            "genres": ["Crime", "Mystery", "Thriller"],
            "description": "A series of mysterious events change the life of a blind pianist who now must report a crime that was actually never witnessed by him.",
            "year": "2018",
            "director": "Sriram Raghavan",
            "cast": ["Ayushmann Khurrana", "Tabu", "Radhika Apte"],
            "language": "hi",
            "rating": 8.2,
            "cultural_themes": ["Thriller", "Piano", "Mystery", "Dark Comedy"]
        },
        {
            "movie_id": "1009",
            "title": "Super Deluxe",
            "original_title": "Super Deluxe",
            "genres": ["Comedy", "Drama", "Thriller"],
            "description": "An unfaithful newly-wed wife, an estranged father, a priest and an angry son suddenly find themselves in the most unexpected predicaments.",
            "year": "2019",
            "director": "Thiagarajan Kumararaja",
            "cast": ["Vijay Sethupathi", "Fahadh Faasil", "Samantha Akkineni"],
            "language": "ta",
            "rating": 8.3,
            "cultural_themes": ["Tamil Cinema", "Dark Comedy", "Multiple Stories"]
        },
        {
            "movie_id": "1010",
            "title": "Gully Boy",
            "original_title": "Gully Boy",
            "genres": ["Drama", "Music"],
            "description": "A coming-of-age story based on the lives of street rappers in Mumbai.",
            "year": "2019",
            "director": "Zoya Akhtar",
            "cast": ["Ranveer Singh", "Alia Bhatt", "Siddhant Chaturvedi"],
            "language": "hi",
            "rating": 7.9,
            "cultural_themes": ["Hip Hop", "Mumbai Slums", "Music", "Dreams"]
        }
    ]
    
    # Convert to MUSE format
    muse_format = {}
    
    for movie in sample_movies:
        movie_id = movie["movie_id"]
        
        # Create enhanced description
        enhanced_desc = f"{movie['description']} Directed by {movie['director']}. "
        enhanced_desc += f"Starring {', '.join(movie['cast'][:3])}. "
        enhanced_desc += f"Genres: {', '.join(movie['genres'])}. "
        enhanced_desc += f"Released in {movie['year']}."
        
        # Create features string
        features = f"Year: {movie['year']}, Director: {movie['director']}, "
        features += f"Cast: {', '.join(movie['cast'][:3])}, Language: {movie['language']}, "
        features += f"Themes: {', '.join(movie['cultural_themes'])}"
        
        muse_format[movie_id] = {
            "item_id": movie_id,
            "title": movie["title"],
            "categories": movie["genres"],
            "description": movie["description"],
            "new_description": enhanced_desc,
            "price": f"Rating: {movie['rating']}/10",
            "features": features
        }
    
    return muse_format

def create_movie_categories():
    """Create categories mapping for movies"""
    categories = [
        "Action", "Adventure", "Animation", "Biography", "Comedy", "Crime", 
        "Documentary", "Drama", "Family", "Fantasy", "History", "Horror",
        "Music", "Musical", "Mystery", "Romance", "Sci-Fi", "Sport", 
        "Thriller", "War", "Western"
    ]
    return categories

def create_category_to_movies_mapping(movies_data: Dict) -> Dict:
    """Create category to movies mapping"""
    category_mapping = {}
    
    for movie_id, movie_data in movies_data.items():
        categories = movie_data["categories"]
        for category in categories:
            if category not in category_mapping:
                category_mapping[category] = []
            category_mapping[category].append(movie_id)
    
    return category_mapping

def main():
    """Create sample Indian movie dataset files"""
    
    print("Creating sample Indian movie dataset...")
    
    # Create movie dataset
    movies_data = create_sample_indian_movies()
    
    # Save main dataset
    with open('indian_movies_sample.json', 'w', encoding='utf-8') as f:
        json.dump(movies_data, f, ensure_ascii=False, indent=2)
    
    print(f"Created sample dataset with {len(movies_data)} movies")
    
    # Create categories list
    categories = create_movie_categories()
    with open('movie_categories.json', 'w', encoding='utf-8') as f:
        json.dump(categories, f, ensure_ascii=False, indent=2)
    
    print(f"Created categories list with {len(categories)} categories")
    
    # Create category to movies mapping
    category_mapping = create_category_to_movies_mapping(movies_data)
    with open('category2movies.json', 'w', encoding='utf-8') as f:
        json.dump(category_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"Created category mapping with {len(category_mapping)} categories")
    
    # Print sample data
    print("\nSample movie data:")
    sample_movie = list(movies_data.values())[0]
    for key, value in sample_movie.items():
        print(f"  {key}: {value}")
    
    print("\nDataset creation complete!")

if __name__ == "__main__":
    main()
