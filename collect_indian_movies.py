#!/usr/bin/env python3
"""
Script to collect Indian movie data from TMDB API and create a dataset
suitable for the MUSE framework adaptation.
"""

import requests
import json
import time
import os
from typing import Dict, List, Optional
import pandas as pd

class IndianMovieCollector:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.themoviedb.org/3"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Indian production companies and regions
        self.indian_companies = [
            "Yash Raj Films", "Dharma Productions", "Red Chillies Entertainment",
            "Eros International", "T-Series", "Balaji Motion Pictures",
            "Excel Entertainment", "Phantom Films", "Reliance Entertainment"
        ]
        
        # Indian languages
        self.indian_languages = ["hi", "ta", "te", "ml", "kn", "bn", "gu", "mr", "pa"]
        
    def search_movies_by_language(self, language: str, pages: int = 5) -> List[Dict]:
        """Search for movies by Indian language"""
        movies = []
        
        for page in range(1, pages + 1):
            url = f"{self.base_url}/discover/movie"
            params = {
                "with_original_language": language,
                "sort_by": "popularity.desc",
                "page": page,
                "vote_count.gte": 10  # Minimum votes for quality
            }
            
            try:
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()
                
                for movie in data.get("results", []):
                    movies.append(movie)
                    
                time.sleep(0.1)  # Rate limiting
                
            except requests.RequestException as e:
                print(f"Error fetching page {page} for language {language}: {e}")
                continue
                
        return movies
    
    def get_movie_details(self, movie_id: int) -> Optional[Dict]:
        """Get detailed information for a specific movie"""
        url = f"{self.base_url}/movie/{movie_id}"
        params = {
            "append_to_response": "credits,keywords,videos,images"
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Error fetching details for movie {movie_id}: {e}")
            return None
    
    def process_movie_data(self, movie_data: Dict) -> Dict:
        """Process raw TMDB data into MUSE-compatible format"""
        
        # Extract cast (top 5 actors)
        cast = []
        if "credits" in movie_data and "cast" in movie_data["credits"]:
            cast = [actor["name"] for actor in movie_data["credits"]["cast"][:5]]
        
        # Extract director
        director = ""
        if "credits" in movie_data and "crew" in movie_data["credits"]:
            for crew_member in movie_data["credits"]["crew"]:
                if crew_member["job"] == "Director":
                    director = crew_member["name"]
                    break
        
        # Extract genres
        genres = [genre["name"] for genre in movie_data.get("genres", [])]
        
        # Extract keywords for cultural themes
        cultural_themes = []
        if "keywords" in movie_data and "keywords" in movie_data["keywords"]:
            cultural_themes = [kw["name"] for kw in movie_data["keywords"]["keywords"][:5]]
        
        # Create enhanced description
        enhanced_desc = self.create_enhanced_description(movie_data, cast, director)
        
        # Map to MUSE format
        processed_movie = {
            "movie_id": str(movie_data["id"]),
            "title": movie_data.get("title", ""),
            "original_title": movie_data.get("original_title", ""),
            "genres": genres,
            "description": movie_data.get("overview", ""),
            "enhanced_description": enhanced_desc,
            "year": movie_data.get("release_date", "")[:4] if movie_data.get("release_date") else "",
            "director": director,
            "cast": cast,
            "language": movie_data.get("original_language", ""),
            "rating": movie_data.get("vote_average", 0),
            "vote_count": movie_data.get("vote_count", 0),
            "duration": movie_data.get("runtime", 0),
            "popularity": movie_data.get("popularity", 0),
            "cultural_themes": cultural_themes,
            "poster_path": movie_data.get("poster_path", ""),
            "backdrop_path": movie_data.get("backdrop_path", "")
        }
        
        return processed_movie
    
    def create_enhanced_description(self, movie_data: Dict, cast: List[str], director: str) -> str:
        """Create an enhanced description similar to clothing descriptions"""
        base_desc = movie_data.get("overview", "")
        
        enhanced_parts = [base_desc]
        
        if director:
            enhanced_parts.append(f"Directed by {director}.")
        
        if cast:
            enhanced_parts.append(f"Starring {', '.join(cast[:3])}.")
        
        if movie_data.get("genres"):
            genres_str = ", ".join([g["name"] for g in movie_data["genres"]])
            enhanced_parts.append(f"Genres: {genres_str}.")
        
        if movie_data.get("release_date"):
            year = movie_data["release_date"][:4]
            enhanced_parts.append(f"Released in {year}.")
        
        return " ".join(enhanced_parts)
    
    def collect_indian_movies(self, total_movies: int = 1000) -> List[Dict]:
        """Collect Indian movies from various languages"""
        all_movies = []
        movies_per_language = total_movies // len(self.indian_languages)
        
        for language in self.indian_languages:
            print(f"Collecting movies for language: {language}")
            
            # Get basic movie list
            basic_movies = self.search_movies_by_language(language, pages=5)
            
            # Get detailed information for each movie
            for movie in basic_movies[:movies_per_language]:
                detailed_movie = self.get_movie_details(movie["id"])
                if detailed_movie:
                    processed_movie = self.process_movie_data(detailed_movie)
                    all_movies.append(processed_movie)
                    
                time.sleep(0.1)  # Rate limiting
                
                if len(all_movies) >= total_movies:
                    break
            
            if len(all_movies) >= total_movies:
                break
        
        return all_movies
    
    def save_dataset(self, movies: List[Dict], filename: str = "indian_movies_dataset.json"):
        """Save the collected movies in MUSE-compatible format"""
        
        # Convert to MUSE item format
        muse_format = {}
        for movie in movies:
            movie_id = movie["movie_id"]
            muse_format[movie_id] = {
                "item_id": movie_id,
                "title": movie["title"],
                "categories": movie["genres"],
                "description": movie["description"],
                "new_description": movie["enhanced_description"],
                "price": f"Rating: {movie['rating']}/10",  # Use rating as "price"
                "features": f"Year: {movie['year']}, Director: {movie['director']}, Cast: {', '.join(movie['cast'][:3])}"
            }
        
        # Save in JSON format
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(muse_format, f, ensure_ascii=False, indent=2)
        
        print(f"Saved {len(movies)} movies to {filename}")
        
        # Also save detailed format for reference
        detailed_filename = filename.replace('.json', '_detailed.json')
        with open(detailed_filename, 'w', encoding='utf-8') as f:
            json.dump(movies, f, ensure_ascii=False, indent=2)
        
        print(f"Saved detailed data to {detailed_filename}")

def main():
    # Note: You need to get a free API key from TMDB
    api_key = "YOUR_TMDB_API_KEY_HERE"
    
    if api_key == "YOUR_TMDB_API_KEY_HERE":
        print("Please set your TMDB API key in the script")
        print("Get a free API key from: https://www.themoviedb.org/settings/api")
        return
    
    collector = IndianMovieCollector(api_key)
    
    print("Starting Indian movie collection...")
    movies = collector.collect_indian_movies(total_movies=500)
    
    print(f"Collected {len(movies)} movies")
    collector.save_dataset(movies)
    
    print("Dataset creation complete!")

if __name__ == "__main__":
    main()
