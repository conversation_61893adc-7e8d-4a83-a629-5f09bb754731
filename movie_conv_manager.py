# -*- coding: utf-8 -*-
"""
Movie conversation manager adapted from the original MUSE framework.
Orchestrates conversations between user and movie recommendation system.
"""

import base64
import json
import random
from openai import OpenAI
from pydantic import BaseModel
from tqdm import tqdm
from movie_user_chat import MovieUser
from movie_system_chat import MovieR<PERSON>sys

def encode_image(image_path):
    """Encode image to base64"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")
    except FileNotFoundError:
        return None

class MovieConversationManager:
    def __init__(self, user: MovieUser, recsys: MovieRecsys, base_url: str, api_key: str):
        self.user = user
        self.recsys = recsys
        self.conversations = []
        self.last_query = ""
        self.actions = []
        self.client = self.get_client(base_url, api_key)
        self.mentioned_movies = []
        self.mentioned_ids = []
        self.action_conv = []
        self.max_round = 4
        self.current_round = 1
        self.conv_num = 1

    def get_client(self, base_url, api_key):
        """Initialize OpenAI client"""
        client = OpenAI(base_url=base_url, api_key=api_key)
        return client

    def action_control(self, last_action=None):
        """Control conversation flow and action selection"""
        if self.current_round == self.max_round:
            return 'recommend'  # End conversation with recommendation

        # Set chit-chat probabilities by round
        chit_chat_probabilities = {
            2: 0.3,
            3: 0.2,
            4: 0
        }

        # If last action was chit-chat, must recommend this round
        if last_action == 'chit-chat':
            return 'recommend'

        # Otherwise, decide based on probability
        if random.random() < chit_chat_probabilities.get(self.current_round, 0):
            return 'chit-chat'
        else:
            return 'recommend'

    def prepare_conversation(self):
        """Reset conversation state"""
        self.conversations = []
        self.mentioned_movies = []
        self.mentioned_ids = []
        self.current_round = 1
        self.actions = []
        self.action_conv = []
        self.last_query = ""

    def start_conversation(self, user_profile):
        """Start a movie recommendation conversation"""
        profile = user_profile['profile']
        scenario = user_profile['scenario']
        requirement = user_profile['requirements']
        target_movie = user_profile['target_item']

        print(f"Starting conversation for {profile['name']} - {scenario}")
        
        # Prepare conversation
        self.prepare_conversation()
        self.user.clear_user()
        self.user.load_user(scenario, requirement, target_movie)

        # System greeting
        greeting = self.generate_system_greeting()
        self.conversations.append({'Assistant': greeting})
        self.action_conv.append({
            'Assistant': greeting, 
            'Action': 'greeting', 
            'Mentioned_movie': [], 
            'Image': []
        })

        # User initial response
        user_response = self.user.express_movie_preferences(self.conversations)
        self.conversations.append({'User': user_response})
        self.action_conv.append({
            'User': user_response, 
            'Action': 'express_preferences', 
            'Mentioned_movie': [], 
            'Image': []
        })

        # Main conversation loop
        finish_flag = False
        while not finish_flag and self.current_round < self.max_round:
            self.current_round += 1
            finish_flag = self.one_round_conversation()

        # Final recommendation if not already done
        if not finish_flag:
            self.final_recommendation()

        return self.create_conversation_output(user_profile)

    def generate_system_greeting(self):
        """Generate system greeting message"""
        greetings = [
            "Hi! I'm here to help you find the perfect movie to watch. What kind of mood are you in today?",
            "Hello! Looking for a good movie recommendation? Tell me what you're in the mood for!",
            "Hi there! I'd love to help you discover a great movie. What's the occasion?",
            "Welcome! Ready to find your next favorite movie? What are you looking for today?"
        ]
        return random.choice(greetings)

    def one_round_conversation(self):
        """Execute one round of conversation"""
        last_action = self.actions[-1] if self.actions else None
        current_action = self.action_control(last_action)
        self.actions.append(current_action)

        if current_action == 'chit-chat':
            return self.handle_chit_chat()
        elif current_action == 'recommend':
            return self.handle_recommendation()
        
        return False

    def handle_chit_chat(self):
        """Handle chit-chat interaction"""
        # System chit-chat
        system_response = self.recsys.chit_chat_about_movies(self.conversations)
        self.conversations.append({'Assistant': system_response})
        self.action_conv.append({
            'Assistant': system_response,
            'Action': 'chit-chat',
            'Mentioned_movie': [],
            'Image': []
        })

        # User chit-chat response
        user_response = self.user.movie_chit_chat(self.conversations, {})
        self.conversations.append({'User': user_response})
        self.action_conv.append({
            'User': user_response,
            'Action': 'chit-chat',
            'Mentioned_movie': [],
            'Image': []
        })

        return False  # Continue conversation

    def handle_recommendation(self):
        """Handle movie recommendation"""
        # Get movie recommendations
        recommended_movies, query = self.recsys.movie_querier(
            self.last_query, 
            self.conversations, 
            self.mentioned_ids
        )
        
        self.last_query = query

        if not recommended_movies:
            # Fallback recommendation
            system_response = "I'm having trouble finding the perfect match. Could you tell me more about what you're looking for?"
            self.conversations.append({'Assistant': system_response})
            return False

        # Select best movie to recommend
        recommended_movie = recommended_movies[0]
        self.mentioned_movies.append(recommended_movie)
        self.mentioned_ids.append(recommended_movie.get('item_id'))

        # Generate recommendation response
        recommendation_text = self.recsys.recommend_movie(
            self.conversations, 
            self.mentioned_movies, 
            recommended_movie
        )

        self.conversations.append({'Assistant': recommendation_text})
        self.action_conv.append({
            'Assistant': recommendation_text,
            'Action': 'recommend',
            'Mentioned_movie': [recommended_movie.get('item_id')],
            'Image': []
        })

        # User response to recommendation
        if self.is_target_movie(recommended_movie):
            # Accept the recommendation
            user_response = self.user.accept_movie(self.conversations)
            action = 'accept'
            return True  # End conversation
        else:
            # Reject or chit-chat about the recommendation
            if random.random() < 0.7:  # 70% chance to reject
                user_response = self.user.reject_movie(self.conversations, recommended_movie)
                action = 'reject'
            else:
                user_response = self.user.movie_chit_chat(self.conversations, recommended_movie)
                action = 'chit-chat'

        self.conversations.append({'User': user_response})
        self.action_conv.append({
            'User': user_response,
            'Action': action,
            'Mentioned_movie': [],
            'Image': []
        })

        return action == 'accept'

    def is_target_movie(self, recommended_movie):
        """Check if recommended movie matches target"""
        target_id = self.user.target_movie.get('item_id')
        recommended_id = recommended_movie.get('item_id')
        
        # Direct match
        if target_id == recommended_id:
            return True
        
        # Genre similarity check (for more realistic acceptance)
        target_genres = set(self.user.target_movie.get('categories', []))
        recommended_genres = set(recommended_movie.get('categories', []))
        
        # Accept if significant genre overlap and high similarity
        overlap = len(target_genres.intersection(recommended_genres))
        if overlap >= 2 and random.random() < 0.3:  # 30% chance for similar movies
            return True
        
        return False

    def final_recommendation(self):
        """Make final recommendation with target movie"""
        target_movie = self.user.target_movie
        
        recommendation_text = self.recsys.recommend_movie(
            self.conversations,
            self.mentioned_movies,
            target_movie
        )
        
        self.conversations.append({'Assistant': recommendation_text})
        self.action_conv.append({
            'Assistant': recommendation_text,
            'Action': 'final_recommend',
            'Mentioned_movie': [target_movie.get('item_id')],
            'Image': []
        })

        # User accepts final recommendation
        user_response = self.user.accept_movie(self.conversations)
        self.conversations.append({'User': user_response})
        self.action_conv.append({
            'User': user_response,
            'Action': 'accept',
            'Mentioned_movie': [],
            'Image': []
        })

    def create_conversation_output(self, user_profile):
        """Create final conversation output"""
        output = {
            'Persona': user_profile['profile'],
            'Scenario': user_profile['scenario'],
            'Target_movie': user_profile['target_item'].get('item_id'),
            'Mentioned_movies': self.mentioned_ids,
            'Conversations': self.action_conv,
            'Simple_conversations': self.conversations
        }
        
        return output

    def save_conversation(self, conversation_data, conv_id):
        """Save conversation to files"""
        # Save detailed conversation
        with open(f'movie_convs/conv_{conv_id}.json', 'w', encoding='utf-8') as f:
            json.dump(conversation_data, f, ensure_ascii=False, indent=2)
        
        # Save simple conversation
        simple_conv = conversation_data['Simple_conversations']
        with open(f'movie_simple_convs/conv_{conv_id}.json', 'w', encoding='utf-8') as f:
            json.dump(simple_conv, f, ensure_ascii=False, indent=2)
        
        print(f"Saved conversation {conv_id}")

def main():
    """Test the movie conversation manager"""
    # Configuration
    api_base = 'https://api.openai.com/v1'  # Replace with your API base
    api_key = 'your-api-key-here'  # Replace with your API key
    db_path = "movie_database"
    data_path = "indian_movies_sample.json"
    model_name = "sentence-transformers/all-MiniLM-L6-v2"  # Lightweight model for testing
    
    # Initialize components
    user = MovieUser(base_url=api_base, api_key=api_key)
    recsys = MovieRecsys(
        db_path=db_path, 
        data_path=data_path, 
        model_path=model_name, 
        base_url=api_base, 
        api_key=api_key
    )
    conv_manager = MovieConversationManager(
        user=user, 
        recsys=recsys, 
        base_url=api_base, 
        api_key=api_key
    )
    
    # Load user profiles
    try:
        with open('indian_user_profiles.json', 'r', encoding='utf-8') as f:
            user_profiles = json.load(f)
    except FileNotFoundError:
        print("User profiles not found. Please run generate_indian_user_profiles.py first.")
        return
    
    # Create output directories
    import os
    os.makedirs('movie_convs', exist_ok=True)
    os.makedirs('movie_simple_convs', exist_ok=True)
    
    # Generate conversations
    for i, profile in enumerate(user_profiles[:3]):  # Test with first 3 profiles
        try:
            conversation = conv_manager.start_conversation(profile)
            conv_manager.save_conversation(conversation, i+1)
        except Exception as e:
            print(f"Error generating conversation {i+1}: {e}")
            continue
    
    print("Movie conversation generation complete!")

if __name__ == "__main__":
    main()
