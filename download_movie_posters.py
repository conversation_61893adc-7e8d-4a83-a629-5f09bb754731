#!/usr/bin/env python3
"""
Download movie posters for the Indian movie dataset to enable multimodal conversations.
"""

import json
import os
import requests
import time
from typing import Dict, List
from urllib.parse import urlparse
import hashlib

class MoviePosterDownloader:
    def __init__(self, movies_data_path: str = "indian_movies_sample.json"):
        self.movies_data = self.load_movies_data(movies_data_path)
        self.poster_dir = "movie_posters"
        self.create_poster_directory()
        
        # Sample poster URLs for our Indian movies (using placeholder service)
        self.poster_urls = {
            "1001": "https://via.placeholder.com/300x450/FF6B6B/FFFFFF?text=3+Idiots",
            "1002": "https://via.placeholder.com/300x450/4ECDC4/FFFFFF?text=Dangal", 
            "1003": "https://via.placeholder.com/300x450/45B7D1/FFFFFF?text=Baahubali",
            "1004": "https://via.placeholder.com/300x450/96CEB4/FFFFFF?text=ZNMD",
            "1005": "https://via.placeholder.com/300x450/FFEAA7/000000?text=Lagaan",
            "1006": "https://via.placeholder.com/300x450/DDA0DD/FFFFFF?text=Queen",
            "1007": "https://via.placeholder.com/300x450/98D8C8/FFFFFF?text=Tumhari+Sulu",
            "1008": "https://via.placeholder.com/300x450/F7DC6F/000000?text=Andhadhun",
            "1009": "https://via.placeholder.com/300x450/BB8FCE/FFFFFF?text=Super+Deluxe",
            "1010": "https://via.placeholder.com/300x450/85C1E9/FFFFFF?text=Gully+Boy"
        }
    
    def load_movies_data(self, path: str) -> Dict:
        """Load movies data"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Movies data file {path} not found.")
            return {}
    
    def create_poster_directory(self):
        """Create directory for movie posters"""
        os.makedirs(self.poster_dir, exist_ok=True)
        print(f"Created poster directory: {self.poster_dir}")
    
    def download_poster(self, movie_id: str, url: str) -> bool:
        """Download a single movie poster"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # Save poster with movie ID as filename
            poster_path = os.path.join(self.poster_dir, f"{movie_id}.jpg")
            
            with open(poster_path, 'wb') as f:
                f.write(response.content)
            
            print(f"Downloaded poster for movie {movie_id}")
            return True
            
        except requests.RequestException as e:
            print(f"Failed to download poster for movie {movie_id}: {e}")
            return False
    
    def download_all_posters(self):
        """Download all movie posters"""
        print("Starting poster download...")
        
        downloaded = 0
        failed = 0
        
        for movie_id, url in self.poster_urls.items():
            if movie_id in self.movies_data:
                if self.download_poster(movie_id, url):
                    downloaded += 1
                else:
                    failed += 1
                
                # Rate limiting
                time.sleep(0.5)
        
        print(f"\nDownload complete!")
        print(f"Successfully downloaded: {downloaded} posters")
        print(f"Failed downloads: {failed} posters")
    
    def create_placeholder_posters(self):
        """Create simple placeholder posters using PIL"""
        try:
            from PIL import Image, ImageDraw, ImageFont
        except ImportError:
            print("PIL not available. Installing...")
            os.system("pip install Pillow")
            from PIL import Image, ImageDraw, ImageFont
        
        print("Creating placeholder movie posters...")
        
        colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
        ]
        
        for i, (movie_id, movie_data) in enumerate(self.movies_data.items()):
            # Create poster image
            img = Image.new('RGB', (300, 450), color=colors[i % len(colors)])
            draw = ImageDraw.Draw(img)
            
            # Add movie title
            title = movie_data.get('title', 'Unknown Movie')
            
            # Try to use a font, fallback to default
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            # Calculate text position
            bbox = draw.textbbox((0, 0), title, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (300 - text_width) // 2
            y = (450 - text_height) // 2
            
            # Draw text with outline for better visibility
            outline_color = "#000000" if colors[i % len(colors)] != "#FFEAA7" else "#FFFFFF"
            text_color = "#FFFFFF" if colors[i % len(colors)] != "#FFEAA7" else "#000000"
            
            # Draw outline
            for adj in range(-2, 3):
                for adj2 in range(-2, 3):
                    draw.text((x+adj, y+adj2), title, font=font, fill=outline_color)
            
            # Draw main text
            draw.text((x, y), title, font=font, fill=text_color)
            
            # Add genre info
            genres = ", ".join(movie_data.get('categories', [])[:2])
            if genres:
                try:
                    genre_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
                except:
                    genre_font = ImageFont.load_default()
                
                genre_bbox = draw.textbbox((0, 0), genres, font=genre_font)
                genre_width = genre_bbox[2] - genre_bbox[0]
                genre_x = (300 - genre_width) // 2
                genre_y = y + text_height + 20
                
                # Draw genre outline
                for adj in range(-1, 2):
                    for adj2 in range(-1, 2):
                        draw.text((genre_x+adj, genre_y+adj2), genres, font=genre_font, fill=outline_color)
                
                draw.text((genre_x, genre_y), genres, font=genre_font, fill=text_color)
            
            # Save poster
            poster_path = os.path.join(self.poster_dir, f"{movie_id}.jpg")
            img.save(poster_path, 'JPEG', quality=85)
            
            print(f"Created poster for {title}")
        
        print(f"Created {len(self.movies_data)} placeholder posters")
    
    def verify_posters(self):
        """Verify that all posters exist"""
        missing_posters = []
        
        for movie_id in self.movies_data.keys():
            poster_path = os.path.join(self.poster_dir, f"{movie_id}.jpg")
            if not os.path.exists(poster_path):
                missing_posters.append(movie_id)
        
        if missing_posters:
            print(f"Missing posters for movies: {missing_posters}")
            return False
        else:
            print("All movie posters are available!")
            return True
    
    def update_movies_with_poster_paths(self):
        """Update movies data with poster paths"""
        updated_movies = {}
        
        for movie_id, movie_data in self.movies_data.items():
            updated_movie = movie_data.copy()
            poster_path = os.path.join(self.poster_dir, f"{movie_id}.jpg")
            
            if os.path.exists(poster_path):
                updated_movie['poster_path'] = poster_path
                updated_movie['has_poster'] = True
            else:
                updated_movie['poster_path'] = None
                updated_movie['has_poster'] = False
            
            updated_movies[movie_id] = updated_movie
        
        # Save updated movies data
        output_path = "indian_movies_with_posters.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(updated_movies, f, ensure_ascii=False, indent=2)
        
        print(f"Updated movies data saved to {output_path}")
        return updated_movies

def main():
    """Download movie posters and update dataset"""
    print("Setting up movie posters for multimodal conversations...")
    
    downloader = MoviePosterDownloader()
    
    # Try to download from URLs first
    print("Attempting to download posters from URLs...")
    downloader.download_all_posters()
    
    # Check if we have all posters
    if not downloader.verify_posters():
        print("Some posters missing. Creating placeholder posters...")
        downloader.create_placeholder_posters()
    
    # Verify again
    downloader.verify_posters()
    
    # Update movies data with poster paths
    updated_movies = downloader.update_movies_with_poster_paths()
    
    print(f"\nSetup complete! {len(updated_movies)} movies now have poster support.")
    print("Posters are stored in the 'movie_posters' directory.")
    print("Updated movie data is in 'indian_movies_with_posters.json'")

if __name__ == "__main__":
    main()
