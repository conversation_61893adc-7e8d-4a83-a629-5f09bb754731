# MUSE Framework Analysis for Indian Movie Adaptation

## Framework Overview

The MUSE (Multimodal Conversational Recommendation Dataset) framework is designed to generate synthetic conversational recommendation data. It consists of several key components:

### Core Components

1. **User Simulator (`user_chat.py`)**
   - Simulates user behavior in conversations
   - Handles chit-chat, acceptance, and rejection responses
   - Uses multimodal inputs (text + images)

2. **Recommendation System (`system_chat.py`)**
   - Manages item retrieval using FAISS vector database
   - Generates recommendations based on user queries
   - Handles query clarification and refinement

3. **Conversation Manager (`conv_manager.py`)**
   - Orchestrates conversations between user and system
   - Controls conversation flow and action selection
   - Manages multimodal and text-based dialogue opening

4. **Item Database (`create_local_item_database.py`)**
   - FAISS-based vector database for item storage
   - Uses BGE-M3 embeddings for semantic search
   - Stores item metadata (title, description, categories, features)

### Data Structure

**Current Item Schema:**
```json
{
  "item_id": "unique_identifier",
  "title": "item_name",
  "categories": ["category1", "category2"],
  "description": "basic_description",
  "new_description": "enhanced_description",
  "price": "price_value",
  "features": "item_features"
}
```

**User Profile Schema:**
```json
{
  "profile": {
    "name": "user_name",
    "age": "age",
    "gender": "gender",
    "profession": "job"
  },
  "scenario": "shopping_context",
  "requirements": "user_needs",
  "target_item": "desired_item_data"
}
```

### Conversation Flow

1. **Opening Phase:**
   - 20% chance: Multimodal opening (image-based)
   - 80% chance: Text-based chit-chat opening

2. **Main Conversation:**
   - Max 4 rounds of interaction
   - Action control: chit-chat vs recommendation
   - Dynamic probability adjustment per round

3. **Actions:**
   - `chit-chat`: Casual conversation without recommendations
   - `recommend`: Item recommendation with explanations
   - `accept`: User accepts recommendation
   - `reject`: User rejects with reasons

### Key Features for Indian Adaptation

**Strengths to Leverage:**
- Multimodal support (text + images)
- Scenario-based user profiles
- Dynamic conversation flow
- Vector-based semantic search
- Structured data format

**Areas for Adaptation:**
- Item schema (clothing → movies)
- Scenarios (shopping → movie selection)
- User profiles (Western → Indian)
- Cultural context integration
- Language and terminology

## Adaptation Strategy for Indian Movies

### 1. Movie Item Schema
```json
{
  "movie_id": "unique_identifier",
  "title": "movie_title",
  "hindi_title": "hindi_movie_title",
  "genres": ["genre1", "genre2"],
  "description": "plot_summary",
  "enhanced_description": "detailed_analysis",
  "year": "release_year",
  "director": "director_name",
  "cast": ["actor1", "actor2"],
  "language": "primary_language",
  "rating": "imdb_rating",
  "duration": "runtime_minutes",
  "box_office": "collection_data",
  "awards": "awards_won",
  "cultural_themes": ["theme1", "theme2"],
  "poster_url": "poster_image_path"
}
```

### 2. Indian Scenarios
- Festival movie watching (Diwali, Holi, etc.)
- Family gathering entertainment
- Date night movie selection
- Regional cinema exploration
- Bollywood vs regional preferences
- Mood-based recommendations
- Nostalgic movie revisiting

### 3. Indian User Profiles
- Indian names using Faker with Indian locales
- Regional preferences (North/South/East/West India)
- Language preferences (Hindi, Tamil, Telugu, etc.)
- Age-appropriate content preferences
- Family vs individual viewing contexts

### 4. Cultural Adaptations
- Indian festival and cultural references
- Family-oriented recommendation logic
- Regional cinema representation
- Multi-generational viewing preferences
- Traditional vs modern content balance

## Implementation Plan

1. **Data Collection:** Gather Indian movie metadata from public APIs
2. **Schema Adaptation:** Modify item structure for movies
3. **Scenario Creation:** Develop Indian cultural scenarios
4. **Profile Generation:** Create realistic Indian user profiles
5. **Conversation Templates:** Adapt prompts for movie recommendations
6. **Testing:** Generate sample conversations and evaluate quality

## Technical Requirements

- OpenAI API access for LLM interactions
- BGE-M3 model for embeddings
- FAISS for vector database
- Movie poster images for multimodal support
- Indian movie metadata sources (TMDB, IMDB, etc.)
