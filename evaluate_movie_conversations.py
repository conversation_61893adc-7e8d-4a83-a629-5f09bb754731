#!/usr/bin/env python3
"""
Evaluation script for Indian movie recommendation conversations.
Analyzes conversation quality, cultural appropriateness, and recommendation accuracy.
"""

import json
import re
from typing import Dict, List, Tuple
import pandas as pd
from collections import Counter

class MovieConversationEvaluator:
    def __init__(self, conversations_path: str = "sample_movie_conversations.json"):
        self.conversations = self.load_conversations(conversations_path)
        self.movies_data = self.load_movies_data()
        
    def load_conversations(self, path: str) -> List[Dict]:
        """Load conversation data"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Conversations file {path} not found.")
            return []
    
    def load_movies_data(self, path: str = "indian_movies_sample.json") -> Dict:
        """Load movies data"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Movies data file {path} not found.")
            return {}
    
    def evaluate_conversation_quality(self, conversation: Dict) -> Dict:
        """Evaluate overall conversation quality"""
        simple_conv = conversation.get('Simple_conversations', [])
        
        metrics = {
            'total_turns': len(simple_conv),
            'avg_message_length': self.calculate_avg_message_length(simple_conv),
            'conversation_flow': self.evaluate_conversation_flow(simple_conv),
            'naturalness_score': self.evaluate_naturalness(simple_conv),
            'coherence_score': self.evaluate_coherence(simple_conv)
        }
        
        return metrics
    
    def calculate_avg_message_length(self, conversation: List[Dict]) -> float:
        """Calculate average message length"""
        total_length = 0
        message_count = 0
        
        for turn in conversation:
            for role, message in turn.items():
                total_length += len(message.split())
                message_count += 1
        
        return total_length / message_count if message_count > 0 else 0
    
    def evaluate_conversation_flow(self, conversation: List[Dict]) -> float:
        """Evaluate conversation flow (0-1 score)"""
        if len(conversation) < 3:
            return 0.0
        
        flow_score = 0.0
        
        # Check if conversation starts with greeting
        first_turn = conversation[0]
        if 'Assistant' in first_turn and any(word in first_turn['Assistant'].lower() 
                                           for word in ['hi', 'hello', 'welcome']):
            flow_score += 0.2
        
        # Check if user expresses preferences early
        if len(conversation) > 1:
            second_turn = conversation[1]
            if 'User' in second_turn:
                flow_score += 0.2
        
        # Check if there are recommendations
        has_recommendation = any('recommend' in str(turn).lower() for turn in conversation)
        if has_recommendation:
            flow_score += 0.3
        
        # Check if conversation ends with acceptance/conclusion
        last_turn = conversation[-1]
        if 'User' in last_turn and any(word in last_turn['User'].lower() 
                                      for word in ['perfect', 'great', 'thanks', 'excellent']):
            flow_score += 0.3
        
        return min(flow_score, 1.0)
    
    def evaluate_naturalness(self, conversation: List[Dict]) -> float:
        """Evaluate naturalness of conversation (0-1 score)"""
        naturalness_score = 0.0
        total_messages = 0
        
        for turn in conversation:
            for role, message in turn.items():
                total_messages += 1
                
                # Check for natural language patterns
                if self.has_natural_patterns(message):
                    naturalness_score += 0.2
                
                # Check for appropriate length (not too short or too long)
                word_count = len(message.split())
                if 5 <= word_count <= 50:
                    naturalness_score += 0.1
                
                # Check for conversational markers
                if any(marker in message.lower() for marker in 
                      ['i think', 'i feel', 'maybe', 'perhaps', 'actually', 'really']):
                    naturalness_score += 0.1
        
        return min(naturalness_score / total_messages, 1.0) if total_messages > 0 else 0.0
    
    def has_natural_patterns(self, message: str) -> bool:
        """Check if message has natural language patterns"""
        patterns = [
            r'\b(I|i)\'m\b',  # I'm
            r'\b(that|That)\'s\b',  # that's
            r'\b(what|What)\'s\b',  # what's
            r'\b(I|i) (would|will|can|could)\b',  # I would/will/can/could
            r'\b(sounds|looks|seems) (good|great|perfect|interesting)\b'
        ]
        
        return any(re.search(pattern, message) for pattern in patterns)
    
    def evaluate_coherence(self, conversation: List[Dict]) -> float:
        """Evaluate conversation coherence (0-1 score)"""
        if len(conversation) < 2:
            return 0.0
        
        coherence_score = 0.0
        
        # Check topic consistency
        movie_mentions = []
        for turn in conversation:
            for role, message in turn.items():
                # Extract movie-related terms
                movie_terms = re.findall(r'\b(movie|film|watch|recommend|genre|comedy|drama|action)\b', 
                                       message.lower())
                movie_mentions.extend(movie_terms)
        
        # If movie-related terms are consistently mentioned, it's coherent
        if len(movie_mentions) >= len(conversation):
            coherence_score += 0.5
        
        # Check response relevance
        for i in range(1, len(conversation)):
            prev_turn = conversation[i-1]
            curr_turn = conversation[i]
            
            # Simple relevance check based on shared keywords
            prev_words = set(word.lower() for turn in prev_turn.values() 
                           for word in turn.split())
            curr_words = set(word.lower() for turn in curr_turn.values() 
                           for word in turn.split())
            
            overlap = len(prev_words.intersection(curr_words))
            if overlap > 0:
                coherence_score += 0.1
        
        return min(coherence_score, 1.0)
    
    def evaluate_cultural_appropriateness(self, conversation: Dict) -> Dict:
        """Evaluate cultural appropriateness for Indian context"""
        persona = conversation.get('Persona', {})
        simple_conv = conversation.get('Simple_conversations', [])
        
        cultural_score = 0.0
        
        # Check if Indian names are used
        name = persona.get('name', '')
        if self.is_indian_name(name):
            cultural_score += 0.2
        
        # Check for Indian cultural references
        all_text = ' '.join([msg for turn in simple_conv for msg in turn.values()])
        
        indian_terms = ['family', 'bollywood', 'hindi', 'tamil', 'telugu', 'festival', 
                       'diwali', 'holi', 'weekend', 'evening']
        
        found_terms = sum(1 for term in indian_terms if term.lower() in all_text.lower())
        cultural_score += min(found_terms * 0.1, 0.4)
        
        # Check regional appropriateness
        region = persona.get('region', '')
        language = persona.get('language', '')
        if region and language:
            cultural_score += 0.2
        
        # Check scenario appropriateness
        scenario = conversation.get('Scenario', '')
        if any(context in scenario.lower() for context in 
              ['family', 'festival', 'weekend', 'evening', 'friends']):
            cultural_score += 0.2
        
        return {
            'cultural_score': min(cultural_score, 1.0),
            'has_indian_context': found_terms > 0,
            'regional_info_present': bool(region and language)
        }
    
    def is_indian_name(self, name: str) -> bool:
        """Check if name appears to be Indian"""
        indian_name_patterns = [
            r'\b(Singh|Kumar|Sharma|Gupta|Patel|Shah|Reddy|Nair|Iyer|Rao)\b',
            r'\b(Priya|Riya|Arjun|Rahul|Amit|Vikram|Rohit|Karan|Aditya)\b'
        ]
        
        return any(re.search(pattern, name) for pattern in indian_name_patterns)
    
    def evaluate_recommendation_accuracy(self, conversation: Dict) -> Dict:
        """Evaluate recommendation accuracy"""
        target_movie_id = conversation.get('Target_movie')
        mentioned_movies = conversation.get('Mentioned_movies', [])
        
        # Check if target movie was eventually recommended
        target_recommended = target_movie_id in mentioned_movies
        
        # Check recommendation diversity
        unique_recommendations = len(set(mentioned_movies))
        
        # Check if final recommendation matches target
        final_recommendation = mentioned_movies[-1] if mentioned_movies else None
        final_match = final_recommendation == target_movie_id
        
        # Calculate recommendation progression score
        progression_score = 0.0
        if mentioned_movies:
            # Check if recommendations get closer to target
            target_movie = self.movies_data.get(target_movie_id, {})
            target_genres = set(target_movie.get('categories', []))
            
            for movie_id in mentioned_movies:
                movie = self.movies_data.get(movie_id, {})
                movie_genres = set(movie.get('categories', []))
                
                # Calculate genre overlap
                overlap = len(target_genres.intersection(movie_genres))
                if overlap > 0:
                    progression_score += overlap / len(target_genres) if target_genres else 0
        
        progression_score = progression_score / len(mentioned_movies) if mentioned_movies else 0
        
        return {
            'target_recommended': target_recommended,
            'final_match': final_match,
            'recommendation_count': len(mentioned_movies),
            'unique_recommendations': unique_recommendations,
            'progression_score': min(progression_score, 1.0)
        }
    
    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive evaluation report"""
        if not self.conversations:
            return {"error": "No conversations to evaluate"}
        
        all_metrics = []
        
        for i, conversation in enumerate(self.conversations):
            conv_metrics = {
                'conversation_id': i + 1,
                'persona_name': conversation.get('Persona', {}).get('name', 'Unknown')
            }
            
            # Quality metrics
            quality_metrics = self.evaluate_conversation_quality(conversation)
            conv_metrics.update(quality_metrics)
            
            # Cultural metrics
            cultural_metrics = self.evaluate_cultural_appropriateness(conversation)
            conv_metrics.update(cultural_metrics)
            
            # Accuracy metrics
            accuracy_metrics = self.evaluate_recommendation_accuracy(conversation)
            conv_metrics.update(accuracy_metrics)
            
            all_metrics.append(conv_metrics)
        
        # Calculate aggregate statistics
        df = pd.DataFrame(all_metrics)
        
        aggregate_stats = {
            'total_conversations': len(all_metrics),
            'avg_conversation_length': df['total_turns'].mean(),
            'avg_naturalness_score': df['naturalness_score'].mean(),
            'avg_coherence_score': df['coherence_score'].mean(),
            'avg_cultural_score': df['cultural_score'].mean(),
            'target_recommendation_rate': df['target_recommended'].mean(),
            'final_match_rate': df['final_match'].mean(),
            'avg_progression_score': df['progression_score'].mean()
        }
        
        return {
            'individual_metrics': all_metrics,
            'aggregate_statistics': aggregate_stats,
            'summary': self.generate_summary(aggregate_stats)
        }
    
    def generate_summary(self, stats: Dict) -> str:
        """Generate evaluation summary"""
        summary = f"""
Indian Movie Recommendation Conversation Evaluation Summary:

📊 Overall Statistics:
- Total conversations evaluated: {stats['total_conversations']}
- Average conversation length: {stats['avg_conversation_length']:.1f} turns
- Average naturalness score: {stats['avg_naturalness_score']:.2f}/1.0
- Average coherence score: {stats['avg_coherence_score']:.2f}/1.0

🇮🇳 Cultural Appropriateness:
- Average cultural score: {stats['avg_cultural_score']:.2f}/1.0

🎯 Recommendation Accuracy:
- Target movie recommendation rate: {stats['target_recommendation_rate']:.1%}
- Final recommendation match rate: {stats['final_match_rate']:.1%}
- Average progression score: {stats['avg_progression_score']:.2f}/1.0

💡 Key Insights:
- Conversations show {'good' if stats['avg_naturalness_score'] > 0.6 else 'moderate'} naturalness
- Cultural context is {'well' if stats['avg_cultural_score'] > 0.6 else 'moderately'} integrated
- Recommendation accuracy is {'high' if stats['final_match_rate'] > 0.7 else 'moderate'}
        """
        
        return summary.strip()

def main():
    """Run comprehensive evaluation"""
    print("Evaluating Indian movie recommendation conversations...")
    
    evaluator = MovieConversationEvaluator()
    report = evaluator.generate_comprehensive_report()
    
    if "error" in report:
        print(f"Error: {report['error']}")
        return
    
    # Print summary
    print(report['summary'])
    
    # Save detailed report
    with open('conversation_evaluation_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\nDetailed evaluation report saved to 'conversation_evaluation_report.json'")
    
    # Create CSV for analysis
    df = pd.DataFrame(report['individual_metrics'])
    df.to_csv('conversation_metrics.csv', index=False)
    print("Individual conversation metrics saved to 'conversation_metrics.csv'")

if __name__ == "__main__":
    main()
