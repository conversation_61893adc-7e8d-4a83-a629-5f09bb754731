#!/usr/bin/env python3
"""
Fetch Bollywood movie datasets and create comprehensive multimodal conversations.
This script provides multiple data sources and fallback options.
"""

import json
import requests
import pandas as pd
import os
from typing import Dict, List, Optional

class BollywoodDatasetFetcher:
    def __init__(self):
        self.dataset_sources = {
            "kaggle_datasets": [
                {
                    "name": "Bollywood Movies Dataset (7,420 movies)",
                    "url": "https://www.kaggle.com/datasets/vidhikishorwaghela/bollywood-movies-dataset",
                    "description": "Comprehensive Bollywood dataset with 7,420 movies for advanced EDA and ML"
                },
                {
                    "name": "Bollywood Movies Dataset (1,698 movies 2005-2017)",
                    "url": "https://www.kaggle.com/datasets/rishidamarla/bollywood-movies-dataset", 
                    "description": "1698 Hindi Movies from 2005-2017 with detailed metadata"
                },
                {
                    "name": "IMDB Bollywood Movies Dataset",
                    "url": "https://www.kaggle.com/datasets/anoopjohny/the-imdb-bollywood-movies-dataset",
                    "description": "Bollywood movies with IMDb ratings and audience data"
                },
                {
                    "name": "Hollywood and Bollywood Movies Dataset",
                    "url": "https://www.kaggle.com/datasets/darpananand07/hollywood-and-bollywood-movies-dataset",
                    "description": "3360 Films from both Hollywood and Bollywood"
                }
            ],
            "api_sources": [
                {
                    "name": "TMDB API",
                    "url": "https://api.themoviedb.org/3",
                    "description": "The Movie Database API with Bollywood movies",
                    "requires_key": True
                },
                {
                    "name": "OMDb API", 
                    "url": "https://www.omdbapi.com/",
                    "description": "Open Movie Database API",
                    "requires_key": True
                }
            ]
        }
        
        # Enhanced Bollywood movie dataset with more movies
        self.enhanced_bollywood_movies = {
            "1001": {
                "title": "3 Idiots",
                "year": "2009",
                "director": "Rajkumar Hirani",
                "cast": ["Aamir Khan", "R. Madhavan", "Sharman Joshi", "Kareena Kapoor"],
                "genres": ["Comedy", "Drama"],
                "language": "Hindi",
                "rating": 8.4,
                "box_office": "₹460 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BNTkyOGVjMGEtNmQzZi00NzFlLTlhOWQtODYyMDc2ZGJmYzFhXkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_SX300.jpg"
            },
            "1002": {
                "title": "Dangal",
                "year": "2016", 
                "director": "Nitesh Tiwari",
                "cast": ["Aamir Khan", "Fatima Sana Shaikh", "Sanya Malhotra"],
                "genres": ["Biography", "Drama", "Sport"],
                "language": "Hindi",
                "rating": 8.3,
                "box_office": "₹2000 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BMTQ4MzQzMzM2Nl5BMl5BanBnXkFtZTgwMTQ1NzU3MDI@._V1_SX300.jpg"
            },
            "1003": {
                "title": "Lagaan",
                "year": "2001",
                "director": "Ashutosh Gowariker", 
                "cast": ["Aamir Khan", "Gracy Singh", "Rachel Shelley"],
                "genres": ["Adventure", "Drama", "Musical"],
                "language": "Hindi",
                "rating": 8.1,
                "box_office": "₹65 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BNDg3NjdiODk5MF5BMl5BanBnXkFtZTcwNjU4MTYyMQ@@._V1_SX300.jpg"
            },
            "1004": {
                "title": "Queen",
                "year": "2013",
                "director": "Vikas Bahl",
                "cast": ["Kangana Ranaut", "Rajkummar Rao", "Lisa Haydon"],
                "genres": ["Comedy", "Drama"],
                "language": "Hindi", 
                "rating": 8.2,
                "box_office": "₹61 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BMTMxOTMwNDU4MF5BMl5BanBnXkFtZTgwNzAyODI1MTE@._V1_SX300.jpg"
            },
            "1005": {
                "title": "Zindagi Na Milegi Dobara",
                "year": "2011",
                "director": "Zoya Akhtar",
                "cast": ["Hrithik Roshan", "Farhan Akhtar", "Abhay Deol", "Katrina Kaif"],
                "genres": ["Adventure", "Comedy", "Drama"],
                "language": "Hindi",
                "rating": 8.2,
                "box_office": "₹153 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BNDMzNjU3YzYtZjBmMS00OGZmLWJkNzYtYWE0ZjdmMGQ2YWZjXkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_SX300.jpg"
            },
            "1006": {
                "title": "Gully Boy",
                "year": "2019",
                "director": "Zoya Akhtar",
                "cast": ["Ranveer Singh", "Alia Bhatt", "Siddhant Chaturvedi"],
                "genres": ["Drama", "Music"],
                "language": "Hindi",
                "rating": 7.9,
                "box_office": "₹238 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BOTllNzJiZjYtZGNkZC00ZmZiLWFkOTEtMWMyNTA1ZTU3YmJhXkEyXkFqcGdeQXVyNjkwOTg4MTA@._V1_SX300.jpg"
            },
            "1007": {
                "title": "Andhadhun",
                "year": "2018",
                "director": "Sriram Raghavan",
                "cast": ["Ayushmann Khurrana", "Tabu", "Radhika Apte"],
                "genres": ["Crime", "Mystery", "Thriller"],
                "language": "Hindi",
                "rating": 8.2,
                "box_office": "₹456 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BYjFjMTQzY2EtZjQ5MC00NGUyLWJiYWMtZDI3MTQ1MGU4OGY2XkEyXkFqcGdeQXVyNjkwOTg4MTA@._V1_SX300.jpg"
            },
            "1008": {
                "title": "Tumhari Sulu",
                "year": "2017",
                "director": "Suresh Triveni",
                "cast": ["Vidya Balan", "Manav Kaul", "Neha Dhupia"],
                "genres": ["Comedy", "Drama", "Family"],
                "language": "Hindi",
                "rating": 7.1,
                "box_office": "₹38 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BMjI5MTg1Njg0Ml5BMl5BanBnXkFtZTgwNjQ4MzU5NDM@._V1_SX300.jpg"
            },
            "1009": {
                "title": "Pink",
                "year": "2016",
                "director": "Aniruddha Roy Chowdhury",
                "cast": ["Amitabh Bachchan", "Taapsee Pannu", "Kirti Kulhari"],
                "genres": ["Crime", "Drama", "Thriller"],
                "language": "Hindi",
                "rating": 8.1,
                "box_office": "₹65 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BYzdhNzQ1NTctNDg5MS00MTYyLWE3MWEtYzQ1ZGM4MGEwOTNiXkEyXkFqcGdeQXVyNDUzOTQ5MjY@._V1_SX300.jpg"
            },
            "1010": {
                "title": "Sholay",
                "year": "1975",
                "director": "Ramesh Sippy",
                "cast": ["Dharmendra", "Amitabh Bachchan", "Sanjeev Kumar", "Hema Malini"],
                "genres": ["Action", "Adventure", "Drama"],
                "language": "Hindi",
                "rating": 8.1,
                "box_office": "₹35 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BYjBkOGRjNzQtYmYyZC00NzU5LWE3YjYtZTdkN2ZkNmY2ODJiXkEyXkFqcGdeQXVyNjc5Mjg4Nzc@._V1_SX300.jpg"
            },
            "1011": {
                "title": "Taare Zameen Par",
                "year": "2007",
                "director": "Aamir Khan",
                "cast": ["Aamir Khan", "Darsheel Safary", "Tisca Chopra"],
                "genres": ["Drama", "Family"],
                "language": "Hindi",
                "rating": 8.4,
                "box_office": "₹71 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BMDhjZWViN2MtNzgxOS00NmI2LWJhNzYtMmZiYTdmOTkyMjE1XkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_SX300.jpg"
            },
            "1012": {
                "title": "Mughal-E-Azam",
                "year": "1960",
                "director": "K. Asif",
                "cast": ["Prithviraj Kapoor", "Dilip Kumar", "Madhubala"],
                "genres": ["Drama", "Romance", "War"],
                "language": "Hindi",
                "rating": 8.1,
                "box_office": "₹10.5 crores",
                "poster_url": "https://m.media-amazon.com/images/M/MV5BNTkyOGVjMGEtNmQzZi00NzFlLTlhOWQtODYyMDc2ZGJmYzFhXkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_SX300.jpg"
            }
        }
    
    def print_dataset_sources(self):
        """Print available dataset sources"""
        print("🎬 Available Bollywood Movie Dataset Sources:")
        print("=" * 60)
        
        print("\n📊 Kaggle Datasets:")
        for i, dataset in enumerate(self.dataset_sources["kaggle_datasets"], 1):
            print(f"{i}. {dataset['name']}")
            print(f"   URL: {dataset['url']}")
            print(f"   Description: {dataset['description']}\n")
        
        print("🔗 API Sources:")
        for i, api in enumerate(self.dataset_sources["api_sources"], 1):
            print(f"{i}. {api['name']}")
            print(f"   URL: {api['url']}")
            print(f"   Description: {api['description']}")
            print(f"   Requires API Key: {api['requires_key']}\n")
        
        print("💡 Instructions to Download:")
        print("1. For Kaggle datasets: Create account at kaggle.com and download manually")
        print("2. For API access: Register for free API keys at respective websites")
        print("3. Use the enhanced dataset provided in this script for immediate testing")
    
    def download_movie_posters(self):
        """Download movie posters for the enhanced dataset"""
        print("📥 Downloading Bollywood movie posters...")
        
        poster_dir = "bollywood_posters"
        os.makedirs(poster_dir, exist_ok=True)
        
        downloaded_count = 0
        
        for movie_id, movie_data in self.enhanced_bollywood_movies.items():
            poster_url = movie_data.get("poster_url")
            if poster_url:
                try:
                    response = requests.get(poster_url, timeout=10)
                    response.raise_for_status()
                    
                    poster_path = os.path.join(poster_dir, f"{movie_id}_{movie_data['title'].replace(' ', '_')}.jpg")
                    
                    with open(poster_path, 'wb') as f:
                        f.write(response.content)
                    
                    # Update movie data with local poster path
                    self.enhanced_bollywood_movies[movie_id]['local_poster_path'] = poster_path
                    downloaded_count += 1
                    
                    print(f"✅ Downloaded: {movie_data['title']}")
                    
                except Exception as e:
                    print(f"❌ Failed to download {movie_data['title']}: {e}")
        
        print(f"\n📊 Downloaded {downloaded_count}/{len(self.enhanced_bollywood_movies)} posters")
        return downloaded_count
    
    def create_muse_format_dataset(self):
        """Convert enhanced dataset to MUSE format"""
        muse_format = {}
        
        for movie_id, movie_data in self.enhanced_bollywood_movies.items():
            # Create enhanced description
            enhanced_desc = f"{movie_data.get('title')} ({movie_data.get('year')}) is a {', '.join(movie_data.get('genres', []))} film directed by {movie_data.get('director')}. "
            enhanced_desc += f"Starring {', '.join(movie_data.get('cast', [])[:3])}. "
            enhanced_desc += f"This {movie_data.get('language')} movie was a major success with a box office collection of {movie_data.get('box_office', 'N/A')}."
            
            # Create features string
            features = f"Year: {movie_data.get('year')}, Director: {movie_data.get('director')}, "
            features += f"Cast: {', '.join(movie_data.get('cast', [])[:3])}, Language: {movie_data.get('language')}, "
            features += f"Rating: {movie_data.get('rating')}/10, Box Office: {movie_data.get('box_office', 'N/A')}"
            
            muse_format[movie_id] = {
                "item_id": movie_id,
                "title": movie_data.get('title'),
                "categories": movie_data.get('genres', []),
                "description": f"A {movie_data.get('language')} {', '.join(movie_data.get('genres', []))} movie starring {', '.join(movie_data.get('cast', [])[:2])}.",
                "new_description": enhanced_desc,
                "price": f"Rating: {movie_data.get('rating')}/10",
                "features": features,
                "poster_url": movie_data.get('poster_url'),
                "local_poster_path": movie_data.get('local_poster_path'),
                "has_poster": bool(movie_data.get('local_poster_path'))
            }
        
        return muse_format
    
    def save_enhanced_dataset(self):
        """Save the enhanced Bollywood dataset"""
        muse_dataset = self.create_muse_format_dataset()
        
        # Save MUSE format
        with open('enhanced_bollywood_movies.json', 'w', encoding='utf-8') as f:
            json.dump(muse_dataset, f, ensure_ascii=False, indent=2)
        
        # Save original format
        with open('bollywood_movies_original.json', 'w', encoding='utf-8') as f:
            json.dump(self.enhanced_bollywood_movies, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Saved enhanced dataset with {len(muse_dataset)} movies")
        print("📁 Files created:")
        print("   - enhanced_bollywood_movies.json (MUSE format)")
        print("   - bollywood_movies_original.json (Original format)")
        
        return muse_dataset

def main():
    """Main function to fetch Bollywood datasets"""
    print("🎬 Bollywood Movie Dataset Fetcher")
    print("=" * 50)
    
    fetcher = BollywoodDatasetFetcher()
    
    # Print available sources
    fetcher.print_dataset_sources()
    
    # Download posters
    print("\n" + "="*50)
    downloaded_count = fetcher.download_movie_posters()
    
    # Create and save enhanced dataset
    print("\n" + "="*50)
    dataset = fetcher.save_enhanced_dataset()
    
    print(f"\n🎉 Setup complete!")
    print(f"📊 Enhanced dataset ready with {len(dataset)} Bollywood movies")
    print(f"🖼️ {downloaded_count} movie posters downloaded")
    print(f"📁 Ready for multimodal conversation generation!")
    
    return dataset

if __name__ == "__main__":
    main()
