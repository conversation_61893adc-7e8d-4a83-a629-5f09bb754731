#!/usr/bin/env python3
"""
Generate Indian user profiles for movie recommendation conversations.
Adapted from the original MUSE framework for Indian cultural context.
"""

import json
import random
from faker import Faker
from typing import Dict, List
import pandas as pd

# Initialize Faker with Indian locale
fake = Faker(['hi_IN', 'en_IN'])

class IndianUserProfileGenerator:
    def __init__(self, movies_data_path: str = "indian_movies_sample.json"):
        self.movies_data = self.load_movies_data(movies_data_path)
        
        # Indian names by region and gender
        self.indian_names = {
            'male': {
                'north': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>aru<PERSON>'],
                'south': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
                'west': ['<PERSON>hr<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
                'east': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>h', '<PERSON><PERSON>b', '<PERSON>an', 'Aniket', '<PERSON>bhishek', '<PERSON>umya', '<PERSON>t']
            },
            'female': {
                'north': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>a', '<PERSON>vya', '<PERSON>hreya', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>mran', '<PERSON>vya', '<PERSON><PERSON>i'],
                'south': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>a', 'Radha', 'Geetha', 'Suma', 'Vani', 'Shanti', 'Kamala', 'Devi'],
                'west': ['Riya', 'Nisha', 'Komal', 'Sneha', 'Mansi', 'Pallavi', 'Shweta', 'Deepika', 'Rashmi', 'Swati'],
                'east': ['Ruma', 'Soma', 'Mita', 'Rina', 'Sima', 'Gita', 'Lata', 'Nita', 'Puja', 'Shila']
            }
        }
        
        # Indian professions
        self.indian_professions = [
            'Software Engineer', 'Teacher', 'Doctor', 'Banker', 'Government Officer',
            'Business Owner', 'Accountant', 'Lawyer', 'Engineer', 'Nurse',
            'Student', 'Homemaker', 'Consultant', 'Manager', 'Sales Executive',
            'Journalist', 'Artist', 'Chef', 'Photographer', 'Designer'
        ]
        
        # Indian regions and their characteristics
        self.regions = {
            'north': {
                'states': ['Delhi', 'Punjab', 'Haryana', 'Uttar Pradesh', 'Rajasthan'],
                'languages': ['Hindi', 'Punjabi', 'Urdu'],
                'movie_preferences': ['Bollywood', 'Punjabi Cinema']
            },
            'south': {
                'states': ['Tamil Nadu', 'Karnataka', 'Andhra Pradesh', 'Kerala'],
                'languages': ['Tamil', 'Telugu', 'Kannada', 'Malayalam'],
                'movie_preferences': ['Tamil Cinema', 'Telugu Cinema', 'Malayalam Cinema']
            },
            'west': {
                'states': ['Maharashtra', 'Gujarat', 'Goa'],
                'languages': ['Marathi', 'Gujarati', 'Hindi'],
                'movie_preferences': ['Bollywood', 'Marathi Cinema']
            },
            'east': {
                'states': ['West Bengal', 'Odisha', 'Assam'],
                'languages': ['Bengali', 'Odia', 'Assamese'],
                'movie_preferences': ['Bengali Cinema', 'Bollywood']
            }
        }
        
        # Age groups and their movie preferences
        self.age_groups = {
            'young': {'range': (18, 30), 'preferences': ['Action', 'Comedy', 'Romance', 'Thriller']},
            'middle': {'range': (31, 50), 'preferences': ['Drama', 'Family', 'Biography', 'Crime']},
            'senior': {'range': (51, 70), 'preferences': ['Drama', 'Musical', 'Biography', 'Family']}
        }
    
    def load_movies_data(self, path: str) -> Dict:
        """Load movies data from JSON file"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Movies data file {path} not found. Using empty dataset.")
            return {}
    
    def generate_indian_name(self, gender: str, region: str) -> str:
        """Generate an Indian name based on gender and region"""
        if gender in self.indian_names and region in self.indian_names[gender]:
            first_name = random.choice(self.indian_names[gender][region])
            # Add surname based on region
            if region == 'north':
                surnames = ['Sharma', 'Gupta', 'Singh', 'Kumar', 'Agarwal', 'Jain']
            elif region == 'south':
                surnames = ['Reddy', 'Nair', 'Iyer', 'Rao', 'Krishnan', 'Pillai']
            elif region == 'west':
                surnames = ['Patel', 'Shah', 'Desai', 'Joshi', 'Mehta', 'Thakkar']
            else:  # east
                surnames = ['Chatterjee', 'Banerjee', 'Mukherjee', 'Das', 'Roy', 'Sen']
            
            surname = random.choice(surnames)
            return f"{first_name} {surname}"
        else:
            return fake.name()
    
    def select_target_movie(self, age_group: str, region: str) -> Dict:
        """Select a target movie based on user preferences"""
        if not self.movies_data:
            return None
        
        # Get preferred genres for age group
        preferred_genres = self.age_groups[age_group]['preferences']
        
        # Filter movies by preferred genres
        suitable_movies = []
        for movie_id, movie_data in self.movies_data.items():
            movie_genres = movie_data.get('categories', [])
            if any(genre in movie_genres for genre in preferred_genres):
                suitable_movies.append((movie_id, movie_data))
        
        if suitable_movies:
            movie_id, movie_data = random.choice(suitable_movies)
            return movie_data
        else:
            # Fallback to any movie
            movie_id = random.choice(list(self.movies_data.keys()))
            return self.movies_data[movie_id]
    
    def generate_movie_scenario(self, age_group: str, region: str) -> str:
        """Generate a movie-watching scenario based on Indian context"""
        
        scenarios = {
            'young': [
                "Planning a movie night with friends",
                "Looking for a good movie to watch on weekend",
                "Want to watch something entertaining after work",
                "Searching for a movie to watch with college friends",
                "Need a good movie for a date night"
            ],
            'middle': [
                "Looking for a family-friendly movie for weekend",
                "Want to watch something with spouse after kids sleep",
                "Planning a movie night for the family",
                "Looking for a good movie to relax after work",
                "Want to watch something meaningful and engaging"
            ],
            'senior': [
                "Looking for a classic or meaningful movie",
                "Want to watch something with good story and values",
                "Planning to watch a movie with grandchildren",
                "Looking for a movie that brings back memories",
                "Want to watch something inspiring and uplifting"
            ]
        }
        
        # Add regional and cultural contexts
        cultural_contexts = [
            f"Celebrating {random.choice(['Diwali', 'Holi', 'Eid', 'Christmas'])} with family",
            "Weekend movie time with family",
            "Monsoon evening entertainment",
            "Festival holiday movie watching",
            "Sunday family time"
        ]
        
        base_scenario = random.choice(scenarios[age_group])
        if random.random() < 0.3:  # 30% chance to add cultural context
            cultural_context = random.choice(cultural_contexts)
            return f"{base_scenario} during {cultural_context.lower()}"
        
        return base_scenario
    
    def generate_movie_requirements(self, age_group: str, target_movie: Dict) -> str:
        """Generate movie requirements based on user profile"""
        
        movie_genres = target_movie.get('categories', [])
        
        requirements_templates = {
            'young': [
                f"I want to watch a {random.choice(movie_genres).lower()} movie",
                "Looking for something entertaining and engaging",
                "Want a movie with good story and characters",
                "Need something that's not too long and boring"
            ],
            'middle': [
                f"Looking for a good {random.choice(movie_genres).lower()} movie",
                "Want something the whole family can enjoy",
                "Need a movie with meaningful content",
                "Looking for something with good values and message"
            ],
            'senior': [
                f"Want to watch a classic {random.choice(movie_genres).lower()} movie",
                "Looking for something with good story and acting",
                "Need a movie with cultural values",
                "Want something that's not too modern or loud"
            ]
        }
        
        base_requirement = random.choice(requirements_templates[age_group])
        
        # Add specific preferences
        additional_prefs = [
            "with good music",
            "with famous actors",
            "that's not too long",
            "with subtitles available",
            "that's highly rated"
        ]
        
        if random.random() < 0.4:  # 40% chance to add additional preference
            additional = random.choice(additional_prefs)
            return f"{base_requirement} {additional}"
        
        return base_requirement
    
    def generate_user_profile(self) -> Dict:
        """Generate a complete Indian user profile"""
        
        # Select random characteristics
        region = random.choice(list(self.regions.keys()))
        gender = random.choice(['male', 'female'])
        age_group = random.choice(list(self.age_groups.keys()))
        age_range = self.age_groups[age_group]['range']
        age = random.randint(age_range[0], age_range[1])
        
        # Generate profile details
        name = self.generate_indian_name(gender, region)
        profession = random.choice(self.indian_professions)
        state = random.choice(self.regions[region]['states'])
        language = random.choice(self.regions[region]['languages'])
        
        # Select target movie
        target_movie = self.select_target_movie(age_group, region)
        
        # Generate scenario and requirements
        scenario = self.generate_movie_scenario(age_group, region)
        requirements = self.generate_movie_requirements(age_group, target_movie)
        
        # Create profile in MUSE format
        profile = {
            "profile": {
                "name": name,
                "age": age,
                "gender": gender,
                "profession": profession,
                "region": region,
                "state": state,
                "language": language
            },
            "scenario": scenario,
            "requirements": requirements,
            "target_item": target_movie if target_movie else {}
        }
        
        return profile
    
    def generate_multiple_profiles(self, count: int = 50) -> List[Dict]:
        """Generate multiple user profiles"""
        profiles = []
        
        for i in range(count):
            profile = self.generate_user_profile()
            profiles.append(profile)
        
        return profiles
    
    def save_profiles(self, profiles: List[Dict], filename: str = "indian_user_profiles.json"):
        """Save user profiles to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(profiles, f, ensure_ascii=False, indent=2)
        
        print(f"Saved {len(profiles)} user profiles to {filename}")

def main():
    """Generate Indian user profiles for movie recommendations"""
    
    print("Generating Indian user profiles for movie recommendations...")
    
    generator = IndianUserProfileGenerator()
    
    # Generate profiles
    profiles = generator.generate_multiple_profiles(count=20)
    
    # Save profiles
    generator.save_profiles(profiles)
    
    # Print sample profile
    print("\nSample user profile:")
    sample_profile = profiles[0]
    print(json.dumps(sample_profile, indent=2, ensure_ascii=False))
    
    print(f"\nGenerated {len(profiles)} Indian user profiles successfully!")

if __name__ == "__main__":
    main()
