{"Persona": {"name": "<PERSON><PERSON>", "age": 21, "gender": "female", "profession": "Artist", "region": "west", "state": "Maharashtra", "language": "Hindi"}, "Scenario": "Want to watch something entertaining after work during sunday family time", "Target_movie": "1001", "Mentioned_movies": ["1007", "1009", "1001"], "Conversations": [{"Assistant": "Hi there! I'd love to help you discover a great movie. What's the occasion?", "Action": "greeting", "Mentioned_movie": [], "Image": []}, {"User": "Hi! I'm want to watch something entertaining after work during sunday family time. Need something that's not too long and boring.", "Action": "express_preferences", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! <PERSON><PERSON><PERSON> sounds like exactly what you need. It's has a compelling story that you'll really enjoy.", "Action": "recommend", "Mentioned_movie": ["1007"], "Image": []}, {"User": "That's not quite what I'm looking for. Do you have something more drama?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! Super Deluxe sounds like exactly what you need. It's is perfect for your current situation.", "Action": "recommend", "Mentioned_movie": ["1009"], "Image": []}, {"User": "That's not quite what I'm looking for. Do you have something more drama?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend 3 Idiots. It's a Comedy, Drama that has great reviews and perfect for want to watch something entertaining after work during sunday family time.", "Action": "recommend", "Mentioned_movie": ["1001"], "Image": []}, {"User": "Perfect! I've heard good things about 3 Idiots.", "Action": "accept", "Mentioned_movie": [], "Image": []}], "Simple_conversations": [{"Assistant": "Hi there! I'd love to help you discover a great movie. What's the occasion?"}, {"User": "Hi! I'm want to watch something entertaining after work during sunday family time. Need something that's not too long and boring."}, {"Assistant": "Perfect! <PERSON><PERSON><PERSON> sounds like exactly what you need. It's has a compelling story that you'll really enjoy."}, {"User": "That's not quite what I'm looking for. Do you have something more drama?"}, {"Assistant": "Perfect! Super Deluxe sounds like exactly what you need. It's is perfect for your current situation."}, {"User": "That's not quite what I'm looking for. Do you have something more drama?"}, {"Assistant": "Based on what you're looking for, I'd recommend 3 Idiots. It's a Comedy, Drama that has great reviews and perfect for want to watch something entertaining after work during sunday family time."}, {"User": "Perfect! I've heard good things about 3 Idiots."}]}