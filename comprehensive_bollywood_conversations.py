#!/usr/bin/env python3
"""
Generate comprehensive multimodal Bollywood movie conversations following 2025 MUSE framework.
Includes: greeting, chit-chat, recommendation, acceptance/rejection, and multimodal interactions.
"""

import json
import random
import base64
import os
from typing import Dict, List, Optional, Tuple

class ComprehensiveBollywoodConversationGenerator:
    def __init__(self, movies_data_path: str = "enhanced_bollywood_movies.json"):
        self.movies_data = self.load_movies_data(movies_data_path)
        self.conversation_patterns = self.load_conversation_patterns()
        self.user_profiles = self.load_user_profiles()
        
    def load_movies_data(self, path: str) -> Dict:
        """Load Bollywood movies data"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Movies data file {path} not found.")
            return {}
    
    def load_user_profiles(self) -> List[Dict]:
        """Load or create Indian user profiles"""
        try:
            with open('indian_user_profiles.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # Create sample profiles if not found
            return self.create_sample_profiles()
    
    def create_sample_profiles(self) -> List[Dict]:
        """Create sample Indian user profiles"""
        profiles = [
            {
                "profile": {
                    "name": "Priya Sharma",
                    "age": 28,
                    "gender": "female",
                    "profession": "Software Engineer",
                    "region": "north",
                    "state": "Delhi",
                    "language": "Hindi"
                },
                "scenario": "Planning a movie night with friends during Diwali celebration",
                "requirements": "Looking for a feel-good comedy movie that everyone can enjoy",
                "target_item": {"item_id": "1001", "title": "3 Idiots", "categories": ["Comedy", "Drama"]}
            },
            {
                "profile": {
                    "name": "Arjun Reddy",
                    "age": 35,
                    "gender": "male", 
                    "profession": "Doctor",
                    "region": "south",
                    "state": "Andhra Pradesh",
                    "language": "Telugu"
                },
                "scenario": "Weekend family movie time with parents and children",
                "requirements": "Want an inspiring biographical movie with good values",
                "target_item": {"item_id": "1002", "title": "Dangal", "categories": ["Biography", "Drama", "Sport"]}
            },
            {
                "profile": {
                    "name": "Kavya Patel",
                    "age": 24,
                    "gender": "female",
                    "profession": "Teacher",
                    "region": "west",
                    "state": "Gujarat", 
                    "language": "Gujarati"
                },
                "scenario": "Solo movie watching after a stressful week at work",
                "requirements": "Need something empowering and uplifting for women",
                "target_item": {"item_id": "1004", "title": "Queen", "categories": ["Comedy", "Drama"]}
            }
        ]
        return profiles
    
    def load_conversation_patterns(self) -> Dict:
        """Load conversation patterns following 2025 MUSE framework"""
        return {
            "greeting_patterns": [
                "Hi there! I'm your movie recommendation assistant. What brings you here today?",
                "Hello! I'd love to help you find the perfect Bollywood movie. What's the occasion?",
                "Welcome! Ready to discover some amazing Indian cinema? Tell me about your mood!",
                "Hi! I'm here to help you find your next favorite Bollywood film. What are you looking for?"
            ],
            "chit_chat_starters": [
                "That's interesting! I love helping people discover great movies. What kind of films do you usually enjoy?",
                "Sounds like a perfect time for a good movie! Do you prefer recent releases or classic Bollywood?",
                "I can definitely help with that! Are you more into mainstream Bollywood or do you like experimental cinema?",
                "Great choice for movie watching! Do you have any favorite actors or directors?"
            ],
            "recommendation_intros": [
                "Based on what you've told me, I think you'd absolutely love {title}! Here's why:",
                "Perfect! I have the ideal movie for you - {title}. Let me show you:",
                "I think I found exactly what you're looking for! {title} would be perfect because:",
                "You know what? {title} is going to be absolutely perfect for your situation!"
            ],
            "poster_sharing_phrases": [
                "Let me show you the poster - I think you'll love the visual style!",
                "Here's the poster that really captures the essence of this movie:",
                "Take a look at this poster - it perfectly represents what the movie is about:",
                "The poster alone will give you a great sense of the movie's vibe:"
            ],
            "acceptance_responses": [
                "Perfect! {title} looks exactly like what I was hoping for!",
                "Wow! The poster really sells it - I can tell this is going to be amazing!",
                "That's it! {title} is definitely what I want to watch. Thank you!",
                "Excellent choice! I can see from the poster this is exactly my type of movie!"
            ],
            "rejection_responses": [
                "The poster looks good, but I was thinking of something more {preference}.",
                "That's interesting, but I prefer {genre} movies. Do you have something like that?",
                "I can see the appeal, but it's not quite what I'm in the mood for right now.",
                "That looks well-made, but I was hoping for something with a different vibe."
            ]
        }
    
    def encode_image_if_exists(self, image_path: str) -> Optional[str]:
        """Encode image to base64 if it exists"""
        if not image_path or not os.path.exists(image_path):
            return None
        
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            print(f"Error encoding image {image_path}: {e}")
            return None
    
    def generate_comprehensive_conversation(self, user_profile: Dict) -> Dict:
        """Generate a complete conversation following 2025 MUSE framework"""
        
        profile = user_profile['profile']
        scenario = user_profile['scenario']
        target_movie = user_profile['target_item']
        
        conversation = []
        
        # Phase 1: Greeting (System initiates)
        greeting = random.choice(self.conversation_patterns['greeting_patterns'])
        conversation.append({
            'turn': 1,
            'role': 'Assistant',
            'message': greeting,
            'action': 'greeting',
            'phase': 'greeting',
            'image': None,
            'movie_id': None
        })
        
        # Phase 2: User expresses initial preferences
        user_initial = self.generate_user_initial_response(scenario, user_profile['requirements'])
        conversation.append({
            'turn': 2,
            'role': 'User', 
            'message': user_initial,
            'action': 'express_preferences',
            'phase': 'preference_expression',
            'image': None,
            'movie_id': None
        })
        
        # Phase 3: Chit-chat and clarification
        chit_chat = random.choice(self.conversation_patterns['chit_chat_starters'])
        conversation.append({
            'turn': 3,
            'role': 'Assistant',
            'message': chit_chat,
            'action': 'chit_chat',
            'phase': 'chit_chat',
            'image': None,
            'movie_id': None
        })
        
        # Phase 4: User provides more details
        user_details = self.generate_user_detailed_response(target_movie, profile)
        conversation.append({
            'turn': 4,
            'role': 'User',
            'message': user_details,
            'action': 'provide_details',
            'phase': 'clarification',
            'image': None,
            'movie_id': None
        })
        
        # Phase 5: First recommendation (not target movie)
        first_rec_movie = self.select_similar_movie(target_movie)
        if first_rec_movie:
            rec_intro = random.choice(self.conversation_patterns['recommendation_intros']).format(
                title=first_rec_movie['title']
            )
            poster_phrase = random.choice(self.conversation_patterns['poster_sharing_phrases'])
            
            first_rec_message = f"{rec_intro} {poster_phrase}"
            
            # Try to include poster if available
            poster_base64 = None
            if first_rec_movie.get('local_poster_path'):
                poster_base64 = self.encode_image_if_exists(first_rec_movie['local_poster_path'])
            
            conversation.append({
                'turn': 5,
                'role': 'Assistant',
                'message': first_rec_message,
                'action': 'recommend_with_poster',
                'phase': 'first_recommendation',
                'image': poster_base64,
                'movie_id': first_rec_movie['item_id'],
                'movie_title': first_rec_movie['title'],
                'poster_path': first_rec_movie.get('local_poster_path')
            })
            
            # Phase 6: User rejection/feedback
            rejection_response = self.generate_rejection_response(first_rec_movie, target_movie)
            conversation.append({
                'turn': 6,
                'role': 'User',
                'message': rejection_response,
                'action': 'reject',
                'phase': 'feedback',
                'image': None,
                'movie_id': None
            })
        
        # Phase 7: Final recommendation (target movie)
        target_data = self.movies_data.get(target_movie['item_id'])
        if target_data:
            final_rec_intro = random.choice(self.conversation_patterns['recommendation_intros']).format(
                title=target_movie['title']
            )
            
            final_message = f"{final_rec_intro} This {', '.join(target_movie['categories'])} movie is perfect for {scenario.lower()}. "
            final_message += self.generate_movie_description(target_data)
            
            # Include poster if available
            target_poster_base64 = None
            if target_data.get('local_poster_path'):
                target_poster_base64 = self.encode_image_if_exists(target_data['local_poster_path'])
                final_message += f" {random.choice(self.conversation_patterns['poster_sharing_phrases'])}"
            
            conversation.append({
                'turn': 7,
                'role': 'Assistant',
                'message': final_message,
                'action': 'final_recommendation',
                'phase': 'final_recommendation',
                'image': target_poster_base64,
                'movie_id': target_movie['item_id'],
                'movie_title': target_movie['title'],
                'poster_path': target_data.get('local_poster_path')
            })
            
            # Phase 8: User acceptance
            acceptance = random.choice(self.conversation_patterns['acceptance_responses']).format(
                title=target_movie['title']
            )
            conversation.append({
                'turn': 8,
                'role': 'User',
                'message': acceptance,
                'action': 'accept',
                'phase': 'acceptance',
                'image': None,
                'movie_id': None
            })
        
        return {
            'conversation_id': f"bollywood_conv_{random.randint(1000, 9999)}",
            'conversation_type': 'comprehensive_multimodal',
            'persona': profile,
            'scenario': scenario,
            'target_movie': target_movie['item_id'],
            'total_turns': len(conversation),
            'phases': ['greeting', 'preference_expression', 'chit_chat', 'clarification', 
                      'first_recommendation', 'feedback', 'final_recommendation', 'acceptance'],
            'has_images': any(turn.get('image') for turn in conversation),
            'image_count': sum(1 for turn in conversation if turn.get('image')),
            'conversations': conversation
        }
    
    def generate_user_initial_response(self, scenario: str, requirements: str) -> str:
        """Generate user's initial response"""
        templates = [
            f"Hi! I'm {scenario.lower()}. {requirements}.",
            f"Hello! I'm {scenario.lower()} and {requirements.lower()}.",
            f"Hi there! {requirements} I'm {scenario.lower()}.",
            f"Hey! {requirements} for {scenario.lower()}."
        ]
        return random.choice(templates)
    
    def generate_user_detailed_response(self, target_movie: Dict, profile: Dict) -> str:
        """Generate user's detailed response during clarification"""
        target_genres = target_movie.get('categories', [])
        
        responses = [
            f"I really enjoy {target_genres[0].lower()} movies, especially ones with good storytelling and strong characters.",
            f"I prefer {target_genres[0].lower()} films that are well-made and have cultural relevance. I'm from {profile.get('state', 'India')} so I appreciate authentic stories.",
            f"I like {target_genres[0].lower()} movies that are both entertaining and meaningful. Something that the whole family can appreciate.",
            f"I'm drawn to {target_genres[0].lower()} films with great performances and engaging plots. I prefer movies that leave a lasting impact."
        ]
        return random.choice(responses)
    
    def select_similar_movie(self, target_movie: Dict) -> Optional[Dict]:
        """Select a movie similar to target for first recommendation"""
        target_genres = set(target_movie.get('categories', []))
        
        candidates = []
        for movie_id, movie_data in self.movies_data.items():
            if movie_id != target_movie.get('item_id'):
                movie_genres = set(movie_data.get('categories', []))
                if target_genres.intersection(movie_genres):
                    candidates.append(movie_data)
        
        return random.choice(candidates) if candidates else None
    
    def generate_movie_description(self, movie_data: Dict) -> str:
        """Generate compelling movie description"""
        features = movie_data.get('features', '')
        
        # Extract key information
        year = self.extract_info(features, 'Year')
        director = self.extract_info(features, 'Director')
        cast = self.extract_info(features, 'Cast')
        rating = self.extract_info(features, 'Rating')
        
        descriptions = [
            f"Directed by {director} in {year}, this film features an outstanding performance by {cast}. With a rating of {rating}, it's been loved by audiences nationwide.",
            f"This {year} masterpiece by {director} showcases brilliant acting by {cast}. The movie has earned a {rating} rating and is considered a modern classic.",
            f"Featuring {cast} in a career-defining role, this {year} film directed by {director} has captivated audiences with its {rating} rating.",
            f"A {year} gem directed by {director}, starring {cast}. This highly-rated film ({rating}) perfectly captures the essence of Indian cinema."
        ]
        
        return random.choice(descriptions)
    
    def extract_info(self, features: str, key: str) -> str:
        """Extract specific information from features string"""
        try:
            if f"{key}:" in features:
                value = features.split(f"{key}:")[1].split(',')[0].strip()
                return value
        except:
            pass
        return "acclaimed"
    
    def generate_rejection_response(self, recommended_movie: Dict, target_movie: Dict) -> str:
        """Generate user rejection response"""
        target_genres = target_movie.get('categories', [])
        
        rejection_templates = [
            f"That looks interesting, but I was thinking more of a {target_genres[0].lower()} movie. Do you have something like that?",
            f"The poster is nice, but I prefer {target_genres[0].lower()} films. Any recommendations in that genre?",
            f"I can see the appeal, but I'm really in the mood for something more {target_genres[0].lower()}. What else do you have?",
            f"That's a good suggestion, but I was hoping for a {target_genres[0].lower()} movie. Can you recommend something different?"
        ]
        
        return random.choice(rejection_templates)
    
    def generate_multiple_conversations(self, count: int = 5) -> List[Dict]:
        """Generate multiple comprehensive conversations"""
        conversations = []
        
        for i in range(count):
            # Use different profiles or create variations
            profile_index = i % len(self.user_profiles)
            profile = self.user_profiles[profile_index]
            
            # Add some variation to scenarios
            if i > len(self.user_profiles) - 1:
                profile = self.create_variation_profile(profile, i)
            
            conversation = self.generate_comprehensive_conversation(profile)
            conversations.append(conversation)
            
            print(f"Generated comprehensive conversation {i+1}/{count}")
        
        return conversations
    
    def create_variation_profile(self, base_profile: Dict, variation_id: int) -> Dict:
        """Create profile variations"""
        variations = {
            "scenarios": [
                "Monsoon evening movie watching with family",
                "Birthday celebration movie night",
                "Weekend relaxation after busy work week",
                "Festival holiday entertainment with relatives"
            ],
            "requirements": [
                "Want something uplifting and entertaining",
                "Looking for a movie with great music and dance",
                "Need something that's not too heavy or serious",
                "Want a movie that showcases Indian culture beautifully"
            ]
        }
        
        new_profile = base_profile.copy()
        new_profile['scenario'] = variations['scenarios'][variation_id % len(variations['scenarios'])]
        new_profile['requirements'] = variations['requirements'][variation_id % len(variations['requirements'])]
        
        return new_profile

def main():
    """Generate comprehensive Bollywood conversations"""
    print("🎬 Generating Comprehensive Bollywood Movie Conversations")
    print("Following 2025 MUSE Framework with all features")
    print("=" * 70)
    
    generator = ComprehensiveBollywoodConversationGenerator()
    
    # Generate conversations
    conversations = generator.generate_multiple_conversations(count=5)
    
    # Create output directory
    os.makedirs('comprehensive_bollywood_conversations', exist_ok=True)
    
    # Save individual conversations
    for i, conv in enumerate(conversations):
        with open(f'comprehensive_bollywood_conversations/conv_{i+1}.json', 'w', encoding='utf-8') as f:
            json.dump(conv, f, ensure_ascii=False, indent=2)
    
    # Save all conversations
    with open('comprehensive_bollywood_conversations.json', 'w', encoding='utf-8') as f:
        json.dump(conversations, f, ensure_ascii=False, indent=2)
    
    # Print statistics
    total_turns = sum(conv['total_turns'] for conv in conversations)
    total_images = sum(conv['image_count'] for conv in conversations)
    conversations_with_images = sum(1 for conv in conversations if conv['has_images'])
    
    print(f"\n📊 Generation Complete!")
    print(f"✅ Generated {len(conversations)} comprehensive conversations")
    print(f"📝 Total conversation turns: {total_turns}")
    print(f"🖼️ Total images included: {total_images}")
    print(f"🎨 Conversations with images: {conversations_with_images}/{len(conversations)}")
    print(f"📁 Files saved in 'comprehensive_bollywood_conversations/' directory")
    
    # Show sample conversation
    if conversations:
        print(f"\n🎭 Sample Conversation Preview:")
        sample = conversations[0]
        print(f"Persona: {sample['persona']['name']} ({sample['persona']['profession']})")
        print(f"Scenario: {sample['scenario']}")
        print(f"Target Movie: {sample['target_movie']}")
        print(f"Total Turns: {sample['total_turns']}")
        print(f"Has Images: {sample['has_images']}")
        
        print(f"\nFirst few turns:")
        for turn in sample['conversations'][:3]:
            print(f"Turn {turn['turn']} ({turn['role']}): {turn['message'][:100]}...")
            if turn.get('image'):
                print(f"  [Includes movie poster image]")
    
    return conversations

if __name__ == "__main__":
    main()
