[{"Persona": {"name": "<PERSON><PERSON>", "age": 21, "gender": "female", "profession": "Artist", "region": "west", "state": "Maharashtra", "language": "Hindi"}, "Scenario": "Want to watch something entertaining after work during sunday family time", "Target_movie": "1001", "Mentioned_movies": ["1007", "1009", "1001"], "Conversations": [{"Assistant": "Hi there! I'd love to help you discover a great movie. What's the occasion?", "Action": "greeting", "Mentioned_movie": [], "Image": []}, {"User": "Hi! I'm want to watch something entertaining after work during sunday family time. Need something that's not too long and boring.", "Action": "express_preferences", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! <PERSON><PERSON><PERSON> sounds like exactly what you need. It's has a compelling story that you'll really enjoy.", "Action": "recommend", "Mentioned_movie": ["1007"], "Image": []}, {"User": "That's not quite what I'm looking for. Do you have something more drama?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! Super Deluxe sounds like exactly what you need. It's is perfect for your current situation.", "Action": "recommend", "Mentioned_movie": ["1009"], "Image": []}, {"User": "That's not quite what I'm looking for. Do you have something more drama?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend 3 Idiots. It's a Comedy, Drama that has great reviews and perfect for want to watch something entertaining after work during sunday family time.", "Action": "recommend", "Mentioned_movie": ["1001"], "Image": []}, {"User": "Perfect! I've heard good things about 3 Idiots.", "Action": "accept", "Mentioned_movie": [], "Image": []}], "Simple_conversations": [{"Assistant": "Hi there! I'd love to help you discover a great movie. What's the occasion?"}, {"User": "Hi! I'm want to watch something entertaining after work during sunday family time. Need something that's not too long and boring."}, {"Assistant": "Perfect! <PERSON><PERSON><PERSON> sounds like exactly what you need. It's has a compelling story that you'll really enjoy."}, {"User": "That's not quite what I'm looking for. Do you have something more drama?"}, {"Assistant": "Perfect! Super Deluxe sounds like exactly what you need. It's is perfect for your current situation."}, {"User": "That's not quite what I'm looking for. Do you have something more drama?"}, {"Assistant": "Based on what you're looking for, I'd recommend 3 Idiots. It's a Comedy, Drama that has great reviews and perfect for want to watch something entertaining after work during sunday family time."}, {"User": "Perfect! I've heard good things about 3 Idiots."}]}, {"Persona": {"name": "<PERSON>", "age": 68, "gender": "female", "profession": "Artist", "region": "south", "state": "Andhra Pradesh", "language": "Malayalam"}, "Scenario": "Planning to watch a movie with grandchildren", "Target_movie": "1005", "Mentioned_movies": ["1007", "1001", "1005"], "Conversations": [{"Assistant": "Hi! I'm here to help you find the perfect movie to watch. What kind of mood are you in today?", "Action": "greeting", "Mentioned_movie": [], "Image": []}, {"User": "Hello! Looking for something with good story and acting for planning to watch a movie with grandchildren.", "Action": "express_preferences", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! <PERSON><PERSON><PERSON> sounds like exactly what you need. It's is perfect for your current situation.", "Action": "recommend", "Mentioned_movie": ["1007"], "Image": []}, {"User": "I'm not really in the mood for comedy. Can you suggest something different?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "How about 3 Idiots? It's a wonderful Comedy, Drama that features excellent performances and fits your mood.", "Action": "recommend", "Mentioned_movie": ["1001"], "Image": []}, {"User": "That's an interesting choice. I like movies with good storytelling.", "Action": "chit-chat", "Mentioned_movie": [], "Image": []}, {"Assistant": "I think you'd really enjoy <PERSON><PERSON><PERSON>. This Adventure, Drama, Musical movie is highly rated and exactly what you're looking for.", "Action": "recommend", "Mentioned_movie": ["1005"], "Image": []}, {"User": "Great recommendation! <PERSON><PERSON><PERSON> is exactly what I was looking for.", "Action": "accept", "Mentioned_movie": [], "Image": []}], "Simple_conversations": [{"Assistant": "Hi! I'm here to help you find the perfect movie to watch. What kind of mood are you in today?"}, {"User": "Hello! Looking for something with good story and acting for planning to watch a movie with grandchildren."}, {"Assistant": "Perfect! <PERSON><PERSON><PERSON> sounds like exactly what you need. It's is perfect for your current situation."}, {"User": "I'm not really in the mood for comedy. Can you suggest something different?"}, {"Assistant": "How about 3 Idiots? It's a wonderful Comedy, Drama that features excellent performances and fits your mood."}, {"User": "That's an interesting choice. I like movies with good storytelling."}, {"Assistant": "I think you'd really enjoy <PERSON><PERSON><PERSON>. This Adventure, Drama, Musical movie is highly rated and exactly what you're looking for."}, {"User": "Great recommendation! <PERSON><PERSON><PERSON> is exactly what I was looking for."}]}, {"Persona": {"name": "<PERSON><PERSON>", "age": 60, "gender": "female", "profession": "Homemaker", "region": "west", "state": "Maharashtra", "language": "Hindi"}, "Scenario": "Want to watch something inspiring and uplifting", "Target_movie": "1002", "Mentioned_movies": ["1007", "1010", "1002"], "Conversations": [{"Assistant": "Hi there! I'd love to help you discover a great movie. What's the occasion?", "Action": "greeting", "Mentioned_movie": [], "Image": []}, {"User": "Want something that's not too modern or loud that's not too long I'm want to watch something inspiring and uplifting.", "Action": "express_preferences", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON><PERSON><PERSON>. It's a Comedy, Drama, Family that is perfect for your current situation.", "Action": "recommend", "Mentioned_movie": ["1007"], "Image": []}, {"User": "I've heard good things about that movie. What else would you recommend?", "Action": "chit-chat", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON><PERSON> Boy. It's a Drama, Music that features excellent performances and fits your mood.", "Action": "recommend", "Mentioned_movie": ["1010"], "Image": []}, {"User": "I'm not really in the mood for drama. Can you suggest something different?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! <PERSON><PERSON> sounds like exactly what you need. It's features excellent performances and fits your mood.", "Action": "recommend", "Mentioned_movie": ["1002"], "Image": []}, {"User": "Great recommendation! <PERSON><PERSON> is exactly what I was looking for.", "Action": "accept", "Mentioned_movie": [], "Image": []}], "Simple_conversations": [{"Assistant": "Hi there! I'd love to help you discover a great movie. What's the occasion?"}, {"User": "Want something that's not too modern or loud that's not too long I'm want to watch something inspiring and uplifting."}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON><PERSON><PERSON>. It's a Comedy, Drama, Family that is perfect for your current situation."}, {"User": "I've heard good things about that movie. What else would you recommend?"}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON><PERSON> Boy. It's a Drama, Music that features excellent performances and fits your mood."}, {"User": "I'm not really in the mood for drama. Can you suggest something different?"}, {"Assistant": "Perfect! <PERSON><PERSON> sounds like exactly what you need. It's features excellent performances and fits your mood."}, {"User": "Great recommendation! <PERSON><PERSON> is exactly what I was looking for."}]}, {"Persona": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 27, "gender": "male", "profession": "Journalist", "region": "east", "state": "Odisha", "language": "Assamese"}, "Scenario": "Searching for a movie to watch with college friends", "Target_movie": "1006", "Mentioned_movies": ["1009", "1002", "1006"], "Conversations": [{"Assistant": "Hi! I'm here to help you find the perfect movie to watch. What kind of mood are you in today?", "Action": "greeting", "Mentioned_movie": [], "Image": []}, {"User": "I'm searching for a movie to watch with college friends and want a movie with good story and characters that's highly rated.", "Action": "express_preferences", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend Super Deluxe. It's a Comedy, Drama, Thriller that is highly rated and exactly what you're looking for.", "Action": "recommend", "Mentioned_movie": ["1009"], "Image": []}, {"User": "That's not quite what I'm looking for. Do you have something more drama?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON><PERSON>. It's a Biography, Drama, Sport that is highly rated and exactly what you're looking for.", "Action": "recommend", "Mentioned_movie": ["1002"], "Image": []}, {"User": "That's not quite what I'm looking for. Do you have something more comedy?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "How about <PERSON>? It's a wonderful Comedy, Drama that features excellent performances and fits your mood.", "Action": "recommend", "Mentioned_movie": ["1006"], "Image": []}, {"User": "Excellent choice! I'll add Queen to my watchlist.", "Action": "accept", "Mentioned_movie": [], "Image": []}], "Simple_conversations": [{"Assistant": "Hi! I'm here to help you find the perfect movie to watch. What kind of mood are you in today?"}, {"User": "I'm searching for a movie to watch with college friends and want a movie with good story and characters that's highly rated."}, {"Assistant": "Based on what you're looking for, I'd recommend Super Deluxe. It's a Comedy, Drama, Thriller that is highly rated and exactly what you're looking for."}, {"User": "That's not quite what I'm looking for. Do you have something more drama?"}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON><PERSON>. It's a Biography, Drama, Sport that is highly rated and exactly what you're looking for."}, {"User": "That's not quite what I'm looking for. Do you have something more comedy?"}, {"Assistant": "How about <PERSON>? It's a wonderful Comedy, Drama that features excellent performances and fits your mood."}, {"User": "Excellent choice! I'll add Queen to my watchlist."}]}, {"Persona": {"name": "<PERSON><PERSON>", "age": 61, "gender": "female", "profession": "Sales Executive", "region": "west", "state": "Goa", "language": "Marathi"}, "Scenario": "Planning to watch a movie with grandchildren", "Target_movie": "1004", "Mentioned_movies": ["1003", "1006", "1004"], "Conversations": [{"Assistant": "Welcome! Ready to find your next favorite movie? What are you looking for today?", "Action": "greeting", "Mentioned_movie": [], "Image": []}, {"User": "I'm planning to watch a movie with grandchildren and want something that's not too modern or loud.", "Action": "express_preferences", "Mentioned_movie": [], "Image": []}, {"Assistant": "I think you'd really enjoy <PERSON>ahu<PERSON>i: The Beginning. This Action, Drama, Fantasy movie has great reviews and perfect for planning to watch a movie with grandchildren.", "Action": "recommend", "Mentioned_movie": ["1003"], "Image": []}, {"User": "That sounds interesting, but I prefer adventure movies. Any other options?", "Action": "chit-chat", "Mentioned_movie": [], "Image": []}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON>. It's a Comedy, Drama that is highly rated and exactly what you're looking for.", "Action": "recommend", "Mentioned_movie": ["1006"], "Image": []}, {"User": "I'm not really in the mood for comedy. Can you suggest something different?", "Action": "reject", "Mentioned_movie": [], "Image": []}, {"Assistant": "Perfect! Zindagi Na Mile<PERSON> sounds like exactly what you need. It's has a compelling story that you'll really enjoy.", "Action": "recommend", "Mentioned_movie": ["1004"], "Image": []}, {"User": "Excellent choice! I'll add <PERSON><PERSON><PERSON> to my watchlist.", "Action": "accept", "Mentioned_movie": [], "Image": []}], "Simple_conversations": [{"Assistant": "Welcome! Ready to find your next favorite movie? What are you looking for today?"}, {"User": "I'm planning to watch a movie with grandchildren and want something that's not too modern or loud."}, {"Assistant": "I think you'd really enjoy <PERSON>ahu<PERSON>i: The Beginning. This Action, Drama, Fantasy movie has great reviews and perfect for planning to watch a movie with grandchildren."}, {"User": "That sounds interesting, but I prefer adventure movies. Any other options?"}, {"Assistant": "Based on what you're looking for, I'd recommend <PERSON>. It's a Comedy, Drama that is highly rated and exactly what you're looking for."}, {"User": "I'm not really in the mood for comedy. Can you suggest something different?"}, {"Assistant": "Perfect! Zindagi Na Mile<PERSON> sounds like exactly what you need. It's has a compelling story that you'll really enjoy."}, {"User": "Excellent choice! I'll add <PERSON><PERSON><PERSON> to my watchlist."}]}]