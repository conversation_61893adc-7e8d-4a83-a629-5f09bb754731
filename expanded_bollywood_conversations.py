#!/usr/bin/env python3
"""
Generate comprehensive multimodal conversations using the expanded Bollywood dataset.
Uses the enhanced movie collection with clean JSON structure and multiple conversation types.
"""

import json
import os
import random
from typing import Dict, List, Optional
from PIL import Image, ImageDraw, ImageFont

class ExpandedBollywoodConversationGenerator:
    def __init__(self):
        self.poster_dir = "expanded_movie_posters"
        self.thumbnail_dir = os.path.join(self.poster_dir, "thumbnails")
        
        # Create directories
        os.makedirs(self.poster_dir, exist_ok=True)
        os.makedirs(self.thumbnail_dir, exist_ok=True)
        
        # Load the expanded Bollywood dataset from enhanced_poster_system.py
        self.load_expanded_dataset()
        
        # Enhanced user profiles for diverse conversations
        self.user_profiles = [
            {
                "name": "<PERSON><PERSON>", "age": 28, "gender": "female", "profession": "Software Engineer",
                "region": "north", "state": "Delhi", "language": "Hindi",
                "scenario": "Planning a movie night with friends during Diwali celebration",
                "requirements": "Looking for a feel-good comedy movie that everyone can enjoy",
                "mood": "festive", "preferences": ["Comedy", "Drama"]
            },
            {
                "name": "<PERSON><PERSON><PERSON>", "age": 35, "gender": "male", "profession": "Doctor",
                "region": "south", "state": "Andhra Pradesh", "language": "Telugu",
                "scenario": "Weekend family movie time with parents and children",
                "requirements": "Want an inspiring biographical movie with good values",
                "mood": "inspirational", "preferences": ["Biography", "Drama", "Sport"]
            },
            {
                "name": "Kavya Patel", "age": 24, "gender": "female", "profession": "Teacher",
                "region": "west", "state": "Gujarat", "language": "Gujarati",
                "scenario": "Solo movie watching after a stressful week at work",
                "requirements": "Need something empowering and uplifting for women",
                "mood": "empowering", "preferences": ["Drama", "Comedy"]
            },
            {
                "name": "Rohit Singh", "age": 30, "gender": "male", "profession": "Marketing Manager",
                "region": "north", "state": "Punjab", "language": "Hindi",
                "scenario": "Date night with girlfriend at home",
                "requirements": "Looking for a romantic movie with good music",
                "mood": "romantic", "preferences": ["Romance", "Musical"]
            },
            {
                "name": "Meera Nair", "age": 26, "gender": "female", "profession": "Journalist",
                "region": "south", "state": "Kerala", "language": "Malayalam",
                "scenario": "Monsoon evening entertainment with family",
                "requirements": "Want something thrilling and engaging",
                "mood": "thrilling", "preferences": ["Thriller", "Crime", "Mystery"]
            },
            {
                "name": "Vikram Joshi", "age": 32, "gender": "male", "profession": "Businessman",
                "region": "west", "state": "Maharashtra", "language": "Marathi",
                "scenario": "Weekend relaxation after busy work week",
                "requirements": "Need high-energy action entertainment",
                "mood": "energetic", "preferences": ["Action", "Adventure"]
            }
        ]
        
        # Conversation patterns following 2025 MUSE framework
        self.conversation_patterns = {
            "greetings": [
                "Hi there! I'm your Bollywood movie recommendation assistant. What brings you here today?",
                "Hello! Ready to discover some amazing Indian cinema? Tell me about your mood!",
                "Welcome! I'd love to help you find the perfect Bollywood movie. What's the occasion?",
                "Hi! I'm here to help you find your next favorite Bollywood film. What are you looking for?"
            ],
            "chit_chat": [
                "That sounds like a perfect time for a good movie! Do you prefer recent releases or classic Bollywood?",
                "Great choice for movie watching! Are you more into mainstream Bollywood or experimental cinema?",
                "I love helping people discover great movies. What kind of films do you usually enjoy?",
                "Interesting! Do you have any favorite actors or directors I should keep in mind?"
            ],
            "poster_sharing": [
                "Let me show you the poster - I think you'll love the visual style!",
                "Here's the poster that really captures the essence of this movie:",
                "Take a look at this poster - it perfectly represents what the movie is about:",
                "The poster alone will give you a great sense of the movie's vibe:"
            ],
            "acceptance": [
                "Perfect! {title} looks exactly like what I was hoping for!",
                "Wow! The poster really sells it - I can tell this is going to be amazing!",
                "That's it! {title} is definitely what I want to watch. Thank you!",
                "Excellent choice! I can see from the poster this is exactly my type of movie!"
            ],
            "rejection": [
                "The poster looks good, but I was thinking of something more {preference}.",
                "That's interesting, but I prefer {genre} movies. Do you have something like that?",
                "I can see the appeal, but it's not quite what I'm in the mood for right now.",
                "That looks well-made, but I was hoping for something with a different vibe."
            ]
        }
    
    def load_expanded_dataset(self):
        """Load the expanded Bollywood dataset"""
        # This mirrors the expanded dataset from enhanced_poster_system.py
        self.bollywood_movies_extended = {
            # Classic Bollywood
            "1001": {"title": "3 Idiots", "year": 2009, "genres": ["Comedy", "Drama"], "color": "#FF6B6B"},
            "1002": {"title": "Dangal", "year": 2016, "genres": ["Biography", "Drama", "Sport"], "color": "#4ECDC4"},
            "1003": {"title": "Lagaan", "year": 2001, "genres": ["Adventure", "Drama", "Musical"], "color": "#45B7D1"},
            "1004": {"title": "Queen", "year": 2013, "genres": ["Comedy", "Drama"], "color": "#96CEB4"},
            "1005": {"title": "Zindagi Na Milegi Dobara", "year": 2011, "genres": ["Adventure", "Comedy", "Drama"], "color": "#FFEAA7"},
            "1006": {"title": "Gully Boy", "year": 2019, "genres": ["Drama", "Music"], "color": "#85C1E9"},
            "1007": {"title": "Andhadhun", "year": 2018, "genres": ["Crime", "Mystery", "Thriller"], "color": "#BB8FCE"},
            "1008": {"title": "Tumhari Sulu", "year": 2017, "genres": ["Comedy", "Drama", "Family"], "color": "#F8C471"},
            "1009": {"title": "Pink", "year": 2016, "genres": ["Crime", "Drama", "Thriller"], "color": "#EC7063"},
            "1010": {"title": "Sholay", "year": 1975, "genres": ["Action", "Adventure", "Drama"], "color": "#58D68D"},
            "1011": {"title": "Taare Zameen Par", "year": 2007, "genres": ["Drama", "Family"], "color": "#5DADE2"},
            "1012": {"title": "Mughal-E-Azam", "year": 1960, "genres": ["Drama", "Romance", "War"], "color": "#AF7AC5"},
            
            # Recent Bollywood Hits
            "1013": {"title": "Baahubali: The Beginning", "year": 2015, "genres": ["Action", "Adventure", "Drama"], "color": "#F39C12"},
            "1014": {"title": "Baahubali 2: The Conclusion", "year": 2017, "genres": ["Action", "Adventure", "Drama"], "color": "#E67E22"},
            "1015": {"title": "KGF: Chapter 1", "year": 2018, "genres": ["Action", "Crime", "Drama"], "color": "#8E44AD"},
            "1016": {"title": "KGF: Chapter 2", "year": 2022, "genres": ["Action", "Crime", "Drama"], "color": "#9B59B6"},
            "1017": {"title": "RRR", "year": 2022, "genres": ["Action", "Adventure", "Drama"], "color": "#E74C3C"},
            "1018": {"title": "Pushpa: The Rise", "year": 2021, "genres": ["Action", "Crime", "Drama"], "color": "#27AE60"},
            "1019": {"title": "Sooryavanshi", "year": 2021, "genres": ["Action", "Crime", "Thriller"], "color": "#F1C40F"},
            "1020": {"title": "83", "year": 2021, "genres": ["Biography", "Drama", "Sport"], "color": "#3498DB"},
            
            # Comedy Classics
            "1021": {"title": "Hera Pheri", "year": 2000, "genres": ["Comedy", "Crime"], "color": "#1ABC9C"},
            "1022": {"title": "Munna Bhai M.B.B.S.", "year": 2003, "genres": ["Comedy", "Drama"], "color": "#16A085"},
            "1023": {"title": "Lage Raho Munna Bhai", "year": 2006, "genres": ["Comedy", "Drama"], "color": "#2ECC71"},
            "1024": {"title": "Golmaal", "year": 2006, "genres": ["Comedy"], "color": "#F4D03F"},
            "1025": {"title": "Welcome", "year": 2007, "genres": ["Action", "Comedy"], "color": "#52BE80"},
            
            # Romance & Drama
            "1026": {"title": "Dilwale Dulhania Le Jayenge", "year": 1995, "genres": ["Drama", "Romance"], "color": "#E8DAEF"},
            "1027": {"title": "Kuch Kuch Hota Hai", "year": 1998, "genres": ["Drama", "Romance"], "color": "#D5A6BD"},
            "1028": {"title": "Kabhi Khushi Kabhie Gham", "year": 2001, "genres": ["Drama", "Family", "Romance"], "color": "#F8BBD9"},
            "1029": {"title": "My Name is Khan", "year": 2010, "genres": ["Drama"], "color": "#AED6F1"},
            "1030": {"title": "Yeh Jawaani Hai Deewani", "year": 2013, "genres": ["Comedy", "Drama", "Romance"], "color": "#A9DFBF"},
            
            # Action & Thriller
            "1031": {"title": "War", "year": 2019, "genres": ["Action", "Adventure", "Thriller"], "color": "#CD6155"},
            "1032": {"title": "Uri: The Surgical Strike", "year": 2019, "genres": ["Action", "Drama", "War"], "color": "#48C9B0"},
            "1033": {"title": "Article 15", "year": 2019, "genres": ["Crime", "Drama", "Thriller"], "color": "#85929E"},
            "1034": {"title": "Scam 1992", "year": 2020, "genres": ["Biography", "Crime", "Drama"], "color": "#F7DC6F"},
            "1035": {"title": "The Family Man", "year": 2019, "genres": ["Action", "Drama", "Thriller"], "color": "#82E0AA"}
        }
        
        print(f"📊 Loaded expanded dataset with {len(self.bollywood_movies_extended)} movies")
    
    def create_movie_poster(self, movie_id: str, movie_data: Dict) -> Dict:
        """Create a movie poster using PIL"""
        try:
            # Create poster image (500x750 standard movie poster ratio)
            img = Image.new('RGB', (500, 750), color=movie_data["color"])
            draw = ImageDraw.Draw(img)
            
            # Try to load fonts
            try:
                title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 32)
                info_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 20)
                small_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
            except:
                title_font = ImageFont.load_default()
                info_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
            
            # Add title with text wrapping for long titles
            title = movie_data["title"]
            if len(title) > 20:
                # Split long titles
                words = title.split()
                if len(words) > 2:
                    title_line1 = " ".join(words[:len(words)//2])
                    title_line2 = " ".join(words[len(words)//2:])
                    
                    # Draw first line
                    title1_bbox = draw.textbbox((0, 0), title_line1, font=title_font)
                    title1_width = title1_bbox[2] - title1_bbox[0]
                    title1_x = (500 - title1_width) // 2
                    
                    # Draw with outline
                    for adj in range(-2, 3):
                        for adj2 in range(-2, 3):
                            draw.text((title1_x + adj, 40 + adj2), title_line1, font=title_font, fill="black")
                    draw.text((title1_x, 40), title_line1, font=title_font, fill="white")
                    
                    # Draw second line
                    title2_bbox = draw.textbbox((0, 0), title_line2, font=title_font)
                    title2_width = title2_bbox[2] - title2_bbox[0]
                    title2_x = (500 - title2_width) // 2
                    
                    for adj in range(-2, 3):
                        for adj2 in range(-2, 3):
                            draw.text((title2_x + adj, 80 + adj2), title_line2, font=title_font, fill="black")
                    draw.text((title2_x, 80), title_line2, font=title_font, fill="white")
                    
                    y_offset = 120
                else:
                    # Single line for shorter titles
                    title_bbox = draw.textbbox((0, 0), title, font=title_font)
                    title_width = title_bbox[2] - title_bbox[0]
                    title_x = (500 - title_width) // 2
                    
                    for adj in range(-2, 3):
                        for adj2 in range(-2, 3):
                            draw.text((title_x + adj, 50 + adj2), title, font=title_font, fill="black")
                    draw.text((title_x, 50), title, font=title_font, fill="white")
                    
                    y_offset = 100
            else:
                # Single line for short titles
                title_bbox = draw.textbbox((0, 0), title, font=title_font)
                title_width = title_bbox[2] - title_bbox[0]
                title_x = (500 - title_width) // 2
                
                for adj in range(-2, 3):
                    for adj2 in range(-2, 3):
                        draw.text((title_x + adj, 50 + adj2), title, font=title_font, fill="black")
                draw.text((title_x, 50), title, font=title_font, fill="white")
                
                y_offset = 100
            
            # Add year
            year_text = f"({movie_data['year']})"
            year_bbox = draw.textbbox((0, 0), year_text, font=info_font)
            year_width = year_bbox[2] - year_bbox[0]
            year_x = (500 - year_width) // 2
            draw.text((year_x, y_offset + 20), year_text, font=info_font, fill="white")
            
            # Add genres
            genres_text = " | ".join(movie_data["genres"][:3])  # Limit to 3 genres
            genres_bbox = draw.textbbox((0, 0), genres_text, font=small_font)
            genres_width = genres_bbox[2] - genres_bbox[0]
            genres_x = (500 - genres_width) // 2
            draw.text((genres_x, y_offset + 60), genres_text, font=small_font, fill="white")
            
            # Add decorative elements
            # Draw border
            draw.rectangle([10, 10, 490, 740], outline="white", width=3)
            
            # Add "BOLLYWOOD" text at bottom
            bollywood_text = "BOLLYWOOD"
            bollywood_bbox = draw.textbbox((0, 0), bollywood_text, font=small_font)
            bollywood_width = bollywood_bbox[2] - bollywood_bbox[0]
            bollywood_x = (500 - bollywood_width) // 2
            draw.text((bollywood_x, 700), bollywood_text, font=small_font, fill="gold")
            
            # Save poster
            poster_filename = f"{movie_id}_{movie_data['title'].replace(' ', '_').replace(':', '')}.jpg"
            poster_path = os.path.join(self.poster_dir, poster_filename)
            img.save(poster_path, "JPEG", quality=90)
            
            # Create thumbnail
            img.thumbnail((150, 225), Image.Resampling.LANCZOS)
            thumbnail_filename = f"{movie_id}_thumb.jpg"
            thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)
            img.save(thumbnail_path, "JPEG", quality=85)
            
            return {
                "filename": poster_filename,
                "path": poster_path,
                "thumbnail": thumbnail_filename,
                "thumbnail_path": thumbnail_path,
                "created": True
            }
            
        except Exception as e:
            print(f"Failed to create poster for {movie_data['title']}: {e}")
            return {"created": False}
    
    def create_all_posters(self) -> Dict:
        """Create posters for all movies in the expanded dataset"""
        print("🎨 Creating movie posters for expanded dataset...")
        poster_metadata = {}
        
        for movie_id, movie_data in self.bollywood_movies_extended.items():
            poster_info = self.create_movie_poster(movie_id, movie_data)
            if poster_info.get("created"):
                poster_metadata[movie_id] = poster_info
                print(f"✅ Created poster: {movie_data['title']}")
            else:
                print(f"❌ Failed: {movie_data['title']}")
        
        print(f"📊 Created {len(poster_metadata)}/{len(self.bollywood_movies_extended)} posters")
        return poster_metadata
    
    def find_movies_by_genre(self, preferred_genres: List[str]) -> List[str]:
        """Find movies matching preferred genres"""
        matching_movies = []
        for movie_id, movie_data in self.bollywood_movies_extended.items():
            movie_genres = movie_data.get("genres", [])
            if any(genre in movie_genres for genre in preferred_genres):
                matching_movies.append(movie_id)
        return matching_movies
    
    def generate_greeting_conversation(self, profile: Dict) -> Dict:
        """Generate greeting-based conversation"""
        # Find suitable movie based on preferences
        matching_movies = self.find_movies_by_genre(profile["preferences"])
        target_movie_id = random.choice(matching_movies) if matching_movies else random.choice(list(self.bollywood_movies_extended.keys()))
        target_movie = self.bollywood_movies_extended[target_movie_id]
        
        return {
            "conversation_id": f"greeting_conv_{random.randint(1000, 9999)}",
            "conversation_type": "greeting_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": random.choice(self.conversation_patterns["greetings"]),
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hi! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": f"Perfect! Based on what you're looking for, I think {target_movie['title']} would be ideal. {random.choice(self.conversation_patterns['poster_sharing'])}",
                    "action": "recommend_with_poster",
                    "phase": "recommendation",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_').replace(':', '')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} ({target_movie['year']}) - {', '.join(target_movie['genres'])} movie poster"
                    }
                },
                {
                    "turn": 4,
                    "role": "User",
                    "message": random.choice(self.conversation_patterns["acceptance"]).format(title=target_movie['title']),
                    "action": "accept",
                    "phase": "acceptance",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 4,
                "has_images": True,
                "image_count": 1,
                "phases": ["greeting", "preference_expression", "recommendation", "acceptance"]
            }
        }
    
    def generate_chitchat_conversation(self, profile: Dict) -> Dict:
        """Generate conversation with chit-chat phase"""
        matching_movies = self.find_movies_by_genre(profile["preferences"])
        target_movie_id = random.choice(matching_movies) if matching_movies else random.choice(list(self.bollywood_movies_extended.keys()))
        target_movie = self.bollywood_movies_extended[target_movie_id]
        
        return {
            "conversation_id": f"chitchat_conv_{random.randint(1000, 9999)}",
            "conversation_type": "chitchat_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": random.choice(self.conversation_patterns["greetings"]),
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hi! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": random.choice(self.conversation_patterns["chit_chat"]),
                    "action": "chit_chat",
                    "phase": "chit_chat",
                    "media": None
                },
                {
                    "turn": 4,
                    "role": "User",
                    "message": f"I really enjoy {', '.join(profile['preferences'][:2]).lower()} movies, especially ones with good storytelling. I prefer films that are both entertaining and meaningful.",
                    "action": "provide_details",
                    "phase": "clarification",
                    "media": None
                },
                {
                    "turn": 5,
                    "role": "Assistant",
                    "message": f"Excellent taste! I have the perfect recommendation for you - {target_movie['title']}. {random.choice(self.conversation_patterns['poster_sharing'])}",
                    "action": "recommend_with_poster",
                    "phase": "recommendation",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_').replace(':', '')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} ({target_movie['year']}) poster showcasing the {', '.join(target_movie['genres']).lower()} elements"
                    }
                },
                {
                    "turn": 6,
                    "role": "User",
                    "message": random.choice(self.conversation_patterns["acceptance"]).format(title=target_movie['title']),
                    "action": "accept",
                    "phase": "acceptance",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 6,
                "has_images": True,
                "image_count": 1,
                "phases": ["greeting", "preference_expression", "chit_chat", "clarification", "recommendation", "acceptance"]
            }
        }
    
    def generate_comparison_conversation(self, profile: Dict) -> Dict:
        """Generate conversation with movie comparison"""
        matching_movies = self.find_movies_by_genre(profile["preferences"])
        
        if len(matching_movies) >= 2:
            target_movie_id = random.choice(matching_movies)
            comparison_movie_id = random.choice([m for m in matching_movies if m != target_movie_id])
        else:
            all_movies = list(self.bollywood_movies_extended.keys())
            target_movie_id = random.choice(all_movies)
            comparison_movie_id = random.choice([m for m in all_movies if m != target_movie_id])
        
        target_movie = self.bollywood_movies_extended[target_movie_id]
        comparison_movie = self.bollywood_movies_extended[comparison_movie_id]
        
        return {
            "conversation_id": f"comparison_conv_{random.randint(1000, 9999)}",
            "conversation_type": "comparison_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": random.choice(self.conversation_patterns["greetings"]),
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hello! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": "Great! I have two excellent options for you. Let me show you both posters so you can see which one appeals to you more.",
                    "action": "show_options",
                    "phase": "option_presentation",
                    "media": None
                },
                {
                    "turn": 4,
                    "role": "Assistant",
                    "message": f"Option 1: {comparison_movie['title']} ({comparison_movie['year']}) - Here's the poster:",
                    "action": "show_poster_option",
                    "phase": "option_1",
                    "media": {
                        "type": "image",
                        "poster_ref": comparison_movie_id,
                        "filename": f"{comparison_movie_id}_{comparison_movie['title'].replace(' ', '_').replace(':', '')}.jpg",
                        "thumbnail": f"{comparison_movie_id}_thumb.jpg",
                        "alt_text": f"{comparison_movie['title']} poster"
                    }
                },
                {
                    "turn": 5,
                    "role": "Assistant",
                    "message": f"Option 2: {target_movie['title']} ({target_movie['year']}) - And here's this poster:",
                    "action": "show_poster_option",
                    "phase": "option_2",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_').replace(':', '')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} poster"
                    }
                },
                {
                    "turn": 6,
                    "role": "User",
                    "message": f"I definitely prefer the second option! {target_movie['title']}'s poster really speaks to me. The visual style and everything about it looks perfect for what I want.",
                    "action": "choose_option",
                    "phase": "selection",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 6,
                "has_images": True,
                "image_count": 2,
                "phases": ["greeting", "preference_expression", "option_presentation", "option_1", "option_2", "selection"]
            }
        }
    
    def generate_rejection_conversation(self, profile: Dict) -> Dict:
        """Generate conversation with rejection and alternative recommendation"""
        matching_movies = self.find_movies_by_genre(profile["preferences"])
        
        if len(matching_movies) >= 2:
            target_movie_id = random.choice(matching_movies)
            rejected_movie_id = random.choice([m for m in matching_movies if m != target_movie_id])
        else:
            all_movies = list(self.bollywood_movies_extended.keys())
            target_movie_id = random.choice(all_movies)
            rejected_movie_id = random.choice([m for m in all_movies if m != target_movie_id])
        
        target_movie = self.bollywood_movies_extended[target_movie_id]
        rejected_movie = self.bollywood_movies_extended[rejected_movie_id]
        
        return {
            "conversation_id": f"rejection_conv_{random.randint(1000, 9999)}",
            "conversation_type": "rejection_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": random.choice(self.conversation_patterns["greetings"]),
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hi! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": f"I think you'd enjoy {rejected_movie['title']}! {random.choice(self.conversation_patterns['poster_sharing'])}",
                    "action": "recommend_with_poster",
                    "phase": "first_recommendation",
                    "media": {
                        "type": "image",
                        "poster_ref": rejected_movie_id,
                        "filename": f"{rejected_movie_id}_{rejected_movie['title'].replace(' ', '_').replace(':', '')}.jpg",
                        "thumbnail": f"{rejected_movie_id}_thumb.jpg",
                        "alt_text": f"{rejected_movie['title']} movie poster"
                    }
                },
                {
                    "turn": 4,
                    "role": "User",
                    "message": random.choice(self.conversation_patterns["rejection"]).format(
                        preference=profile["mood"], 
                        genre=profile["preferences"][0]
                    ),
                    "action": "reject",
                    "phase": "feedback",
                    "media": None
                },
                {
                    "turn": 5,
                    "role": "Assistant",
                    "message": f"I understand! Let me suggest {target_movie['title']} instead - this should be much more aligned with what you're looking for:",
                    "action": "alternative_recommendation",
                    "phase": "final_recommendation",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_').replace(':', '')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} alternative recommendation poster"
                    }
                },
                {
                    "turn": 6,
                    "role": "User",
                    "message": random.choice(self.conversation_patterns["acceptance"]).format(title=target_movie['title']),
                    "action": "accept",
                    "phase": "acceptance",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 6,
                "has_images": True,
                "image_count": 2,
                "phases": ["greeting", "preference_expression", "first_recommendation", "feedback", "final_recommendation", "acceptance"]
            }
        }
    
    def generate_all_conversations(self, count_per_type: int = 2) -> List[Dict]:
        """Generate multiple conversations of different types"""
        conversations = []
        
        conversation_generators = [
            ("greeting", self.generate_greeting_conversation),
            ("chitchat", self.generate_chitchat_conversation),
            ("comparison", self.generate_comparison_conversation),
            ("rejection", self.generate_rejection_conversation)
        ]
        
        for conv_type, generator_func in conversation_generators:
            for i in range(count_per_type):
                profile = random.choice(self.user_profiles)
                conversation = generator_func(profile)
                conversations.append(conversation)
                print(f"✅ Generated {conv_type} conversation: {conversation['conversation_id']}")
        
        return conversations
    
    def save_expanded_dataset(self, poster_metadata: Dict, conversations: List[Dict]):
        """Save the expanded dataset and conversations"""
        
        # Create comprehensive movie dataset
        expanded_movies = {}
        for movie_id, movie_data in self.bollywood_movies_extended.items():
            poster_info = poster_metadata.get(movie_id, {})
            
            expanded_movies[movie_id] = {
                "item_id": movie_id,
                "title": movie_data["title"],
                "year": movie_data["year"],
                "categories": movie_data["genres"],
                "color_theme": movie_data["color"],
                "poster": {
                    "available": poster_info.get("created", False),
                    "filename": poster_info.get("filename"),
                    "thumbnail": poster_info.get("thumbnail"),
                    "path": f"expanded_movie_posters/{poster_info.get('filename', '')}" if poster_info.get("filename") else None
                }
            }
        
        # Save files
        with open('expanded_bollywood_dataset.json', 'w', encoding='utf-8') as f:
            json.dump(expanded_movies, f, ensure_ascii=False, indent=2)
        
        with open('expanded_multimodal_conversations.json', 'w', encoding='utf-8') as f:
            json.dump(conversations, f, ensure_ascii=False, indent=2)
        
        with open('expanded_poster_metadata.json', 'w', encoding='utf-8') as f:
            json.dump(poster_metadata, f, ensure_ascii=False, indent=2)
        
        # Create individual conversation files
        os.makedirs('expanded_conversations', exist_ok=True)
        for i, conv in enumerate(conversations):
            with open(f'expanded_conversations/conv_{i+1}_{conv["conversation_type"]}.json', 'w', encoding='utf-8') as f:
                json.dump(conv, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Saved expanded datasets:")
        print(f"   📊 expanded_bollywood_dataset.json - {len(expanded_movies)} movies")
        print(f"   🎭 expanded_multimodal_conversations.json - {len(conversations)} conversations")
        print(f"   📸 expanded_poster_metadata.json - {len(poster_metadata)} poster records")
        print(f"   📁 expanded_conversations/ - Individual conversation files")

def main():
    """Generate comprehensive conversations with expanded dataset"""
    print("🎬 Expanded Bollywood Multimodal Conversation Generator")
    print("Using Enhanced Dataset with 35+ Movies")
    print("=" * 70)
    
    generator = ExpandedBollywoodConversationGenerator()
    
    # Create movie posters
    poster_metadata = generator.create_all_posters()
    
    # Generate conversations (2 of each type = 8 total)
    print(f"\n🎭 Generating conversations...")
    conversations = generator.generate_all_conversations(count_per_type=2)
    
    # Save everything
    print(f"\n💾 Saving datasets...")
    generator.save_expanded_dataset(poster_metadata, conversations)
    
    # Print comprehensive summary
    total_turns = sum(conv['metadata']['total_turns'] for conv in conversations)
    total_images = sum(conv['metadata']['image_count'] for conv in conversations)
    conversations_with_images = sum(1 for conv in conversations if conv['metadata']['has_images'])
    
    print(f"\n🎉 Generation Complete!")
    print(f"✅ Generated {len(conversations)} comprehensive conversations")
    print(f"📝 Total conversation turns: {total_turns}")
    print(f"🖼️ Total images: {total_images}")
    print(f"🎨 Movie posters created: {len(poster_metadata)}")
    print(f"📊 Conversations with images: {conversations_with_images}/{len(conversations)}")
    print(f"📁 All files use clean references (no base64 strings)")
    
    # Show conversation breakdown
    print(f"\n📋 Conversation Types Generated:")
    type_counts = {}
    for conv in conversations:
        conv_type = conv['conversation_type']
        type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
    
    for conv_type, count in type_counts.items():
        print(f"   • {conv_type}: {count} conversations")
    
    # Show sample conversation
    if conversations:
        print(f"\n🎭 Sample Conversation Preview:")
        sample = conversations[0]
        print(f"Type: {sample['conversation_type']}")
        print(f"Persona: {sample['persona']['name']} ({sample['persona']['profession']})")
        print(f"Scenario: {sample['scenario']}")
        print(f"Target Movie: {generator.bollywood_movies_extended[sample['target_movie']]['title']}")
        print(f"Total Turns: {sample['metadata']['total_turns']}")
        print(f"Images: {sample['metadata']['image_count']}")
    
    return conversations

if __name__ == "__main__":
    main()
