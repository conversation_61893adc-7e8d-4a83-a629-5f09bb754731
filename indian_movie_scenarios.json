{"Festival_Entertainment": ["Di<PERSON>i movie night with family", "Holi celebration movie watching", "Eid special movie time with friends", "Christmas family movie marathon", "Navratri festival entertainment", "<PERSON><PERSON> movie for couples", "<PERSON><PERSON><PERSON> sibling movie time", "<PERSON><PERSON><PERSON> celebration movies", "Durga Puja festival entertainment", "<PERSON><PERSON> family movie gathering", "Pongal celebration movie time", "Baisakhi festival entertainment", "<PERSON><PERSON><PERSON>i devotional movies", "Dussehra victory celebration films"], "Family_Bonding": ["Weekend family movie time", "Sunday afternoon entertainment", "Multi-generational movie watching", "Joint family movie selection", "Grandparents and grandchildren movie time", "Extended family gathering entertainment", "Family reunion movie night", "Three generation movie watching", "Traditional family movie time", "Regional family movie preferences", "Mother-daughter movie bonding", "Father-son movie time", "Sibling movie marathon", "Cousin gathering entertainment"], "Regional_Cinema": ["Exploring Bollywood classics", "South Indian cinema appreciation", "Tamil movie exploration", "Telugu film discovery", "Malayalam cinema experience", "Kannada movie watching", "Bengali film appreciation", "Marathi cinema exploration", "Gujarati movie experience", "Punjabi film entertainment", "Regional language movie night", "Cross-cultural cinema exploration", "Local cinema appreciation", "State-specific movie preferences"], "Mood_Based": ["Monsoon movie watching", "Romantic evening entertainment", "Comedy night with friends", "Emotional drama for reflection", "Action movie for excitement", "Musical movie for entertainment", "Thriller for suspense evening", "Biography for inspiration", "Historical movie for learning", "Feel-good movie after stress", "Motivational movie for encouragement", "Nostalgic movie for memories", "Light-hearted entertainment", "Serious cinema for contemplation"], "Social_Occasions": ["Date night movie selection", "Friends gathering entertainment", "College reunion movie night", "Office party movie screening", "Neighborhood community movie", "Club movie night", "Society gathering entertainment", "Hostel movie marathon", "Apartment complex movie night", "Local community cinema", "Group movie outing", "Social club entertainment", "Cultural society movie screening", "Youth group movie night"], "Life_Events": ["Wedding celebration movies", "Anniversary special movie", "Birthday movie celebration", "Graduation party entertainment", "Retirement celebration movies", "Housewarming movie night", "Job promotion celebration", "Achievement celebration movies", "Milestone birthday entertainment", "Success celebration cinema", "New year movie resolution", "Career change reflection movies", "Life transition entertainment", "Personal growth movie inspiration"], "Seasonal_Preferences": ["Summer vacation movie marathon", "Winter cozy movie nights", "Monsoon indoor entertainment", "Spring festival movies", "Holiday season entertainment", "School vacation movie time", "Office break movie watching", "Weekend leisure entertainment", "Evening relaxation movies", "Night time entertainment", "Afternoon movie break", "Morning feel-good movies", "Late night thriller watching", "Early evening family time"], "Cultural_Exploration": ["Traditional story movies", "Mythology-based cinema", "Historical period films", "Cultural heritage movies", "Folk tale adaptations", "Religious story films", "Epic movie experiences", "Classical literature adaptations", "Cultural documentary watching", "Heritage site movie inspiration", "Traditional art form movies", "Cultural festival films", "Ancient history cinema", "Spiritual journey movies"], "Modern_Lifestyle": ["Urban lifestyle movies", "Metropolitan cinema experience", "Modern relationship movies", "Career-focused films", "Technology-themed movies", "Contemporary social issues", "Modern family dynamics", "Urban youth entertainment", "Professional life movies", "City life cinema", "Modern romance films", "Contemporary drama", "Current affairs movies", "Social media age films"], "Educational_Entertainment": ["Learning through cinema", "Educational movie watching", "Documentary appreciation", "Historical learning movies", "Science-based films", "Biographical learning", "Cultural education movies", "Language learning cinema", "Skill development movies", "Knowledge-based entertainment", "Academic movie discussions", "Research-inspired films", "Educational documentary time", "Learning-focused cinema"]}