# -*- coding: utf-8 -*-
"""
Movie recommendation system adapted from the original MUSE framework.
Handles movie recommendations instead of clothing items.
"""

import json
import random
import base64
from openai import OpenAI
from pydantic import BaseModel
from create_local_item_database import ItemVector
import os

def encode_image(image_path):
    """Encode image to base64 for multimodal inputs"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Image not found: {image_path}")
        return None

class MovieRecsys:
    def __init__(self, db_path: str, data_path: str, model_path: str, base_url: str, api_key: str) -> None:
        self.client = self.get_client(base_url=base_url, api_key=api_key)
        self.db_path = db_path
        self.data_path = data_path
        self.model_path = model_path
        self.item_db = self.get_item_db()
        self.recommended_movies = []
        self.last_query = ""
        self.prompts = self.get_movie_prompts()

    def get_item_db(self):
        """Initialize movie database"""
        myDB = ItemVector(
            db_path=self.db_path,
            model_name=self.model_path,
            llm=None,
            data_path=self.data_path,
            force_create=False,
            use_multi_query=False,
        )
        return myDB

    def get_client(self, base_url, api_key):
        """Initialize OpenAI client"""
        client = OpenAI(base_url=base_url, api_key=api_key)
        return client

    def get_movie_prompts(self):
        """Get movie-specific prompts"""
        prompts = {}
        prompts['chit_chat'] = """You are a friendly movie recommendation assistant. 
        Engage in casual conversation about movies, preferences, and entertainment without making specific recommendations yet."""
        
        prompts['clarify'] = """You are a movie preference clarification assistant. 
        Help users clarify their movie preferences by asking about genres, mood, language, actors, or specific requirements."""
        
        prompts['recommend'] = """You are a movie recommendation assistant. 
        Based on the user's preferences and conversation history, recommend suitable movies with explanations."""
        
        prompts['query'] = """You are a movie search query generator. 
        Generate specific search queries to find movies that match user preferences."""
        
        return prompts

    def clear(self):
        """Clear recommendation history"""
        self.recommended_movies = []
        self.last_query = ""

    def get_movie_requirements(self, conversations):
        """Extract movie requirements from conversation"""
        content_system = """You are a movie preference analyzer. 
        Analyze the conversation between a user and a movie recommendation system.
        Identify and summarize the user's movie preferences including:
        1. Preferred genres (comedy, drama, action, etc.)
        2. Language preferences (Hindi, Tamil, Telugu, etc.)
        3. Mood or occasion (family time, date night, festival, etc.)
        4. Specific actors, directors, or themes mentioned
        5. Any dislikes or restrictions mentioned
        
        Provide a clear summary of the user's movie preferences."""
        
        content_user = f"Conversation Context: {conversations}"

        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.2,
        )

        requirements = response.choices[0].message.content
        return requirements

    def clarify_movie_query(self, new_query):
        """Clarify movie search query"""
        content_system = """You are a movie search query clarification assistant.
        Transform vague movie requests into specific, searchable queries.
        
        Guidelines:
        1. Convert general moods into specific genres
        2. Add context for Indian cinema preferences
        3. Specify language if mentioned
        4. Include cultural context if relevant
        5. Maintain user's original intent
        
        Examples:
        "Something funny" → "Hindi comedy movies, Bollywood humor, family-friendly comedy"
        "Good movie for family" → "Family drama, wholesome entertainment, multi-generational appeal"
        
        Output only the clarified query."""
        
        content_user = f"Initial query: {new_query}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.2,
        )
        
        return response.choices[0].message.content

    def generate_movie_query(self, last_query, new_query):
        """Generate optimized movie search query"""
        content_system = """You are a movie search query optimizer.
        Combine previous search context with new requirements to create an effective search query.
        
        Focus on:
        1. Movie genres and themes
        2. Cultural and language preferences
        3. Mood and occasion context
        4. Specific requirements mentioned
        
        Return only the optimized search query."""
        
        content_user = f"Previous query: {last_query}\nNew requirements: {new_query}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.2,
        )
        
        return response.choices[0].message.content

    def search_movies(self, query, mentioned_ids, k=5):
        """Search for movies using vector database"""
        self.item_db.retriever.search_kwargs = {"k": k}
        
        try:
            result = self.item_db.search_retriever(query)
            if result is None:
                return []
            
            # Filter out already mentioned movies
            filtered_results = []
            for item in result:
                movie_id = item.metadata.get('item_id')
                if movie_id not in mentioned_ids:
                    filtered_results.append(item.metadata)
            
            return filtered_results[:3]  # Return top 3 movies
            
        except Exception as e:
            print(f"Error searching movies: {e}")
            return []

    def movie_querier(self, last_query, conversations, mentioned_ids):
        """Main movie query processing"""
        # Get requirements from conversation
        requirements = self.get_movie_requirements(conversations)
        
        # Clarify the query
        clarified_query = self.clarify_movie_query(requirements)
        
        # Generate optimized search query
        final_query = self.generate_movie_query(last_query, clarified_query)
        
        # Search for movies
        result_movies = self.search_movies(final_query, mentioned_ids)
        
        return result_movies, final_query

    def recommend_movie(self, conversations, mentioned_movies, target_movie):
        """Generate movie recommendation response"""
        content_system = """You are an enthusiastic movie recommendation assistant.
        Based on the conversation history and user preferences, recommend a movie with:
        
        1. Brief explanation of why this movie fits their preferences
        2. Highlight key features (genre, cast, director, themes)
        3. Mention what makes it special or appealing
        4. Keep the tone conversational and engaging
        5. Adapt language to Indian context when appropriate
        
        Be enthusiastic but not overly promotional."""
        
        movie_info = f"""
        Movie: {target_movie.get('title', 'Unknown')}
        Genres: {', '.join(target_movie.get('categories', []))}
        Description: {target_movie.get('description', '')}
        Features: {target_movie.get('features', '')}
        """
        
        content_user = f"Conversation: {conversations}\nMovie to recommend: {movie_info}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.3,
        )
        
        return response.choices[0].message.content

    def chit_chat_about_movies(self, conversations):
        """Generate movie-related chit-chat"""
        content_system = """You are a friendly movie enthusiast having a casual conversation.
        Respond naturally to the user's movie-related comments without making specific recommendations.
        
        You can:
        1. Share general thoughts about movies or genres
        2. Ask follow-up questions about preferences
        3. Comment on movie trends or popular films
        4. Discuss movie-watching experiences
        5. Keep the conversation flowing naturally
        
        Avoid making specific movie recommendations in chit-chat."""
        
        content_user = f"Conversation so far: {conversations}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.4,
        )
        
        return response.choices[0].message.content

    def find_similar_movie(self, target_movie, scenario):
        """Find a movie similar to target for multimodal opening"""
        # For now, return a random movie from database
        # In a real implementation, this would use similarity search
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                movies_data = json.load(f)
            
            # Filter movies by similar genre
            target_genres = target_movie.get('categories', [])
            similar_movies = []
            
            for movie_id, movie_data in movies_data.items():
                if movie_id != target_movie.get('item_id'):
                    movie_genres = movie_data.get('categories', [])
                    if any(genre in movie_genres for genre in target_genres):
                        similar_movies.append(movie_data)
            
            if similar_movies:
                return random.choice(similar_movies)
            else:
                # Fallback to any movie
                return random.choice(list(movies_data.values()))
                
        except Exception as e:
            print(f"Error finding similar movie: {e}")
            return None
