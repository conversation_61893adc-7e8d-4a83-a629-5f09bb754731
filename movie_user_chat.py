# -*- coding: utf-8 -*-
"""
Movie user simulator adapted from the original MUSE framework.
Simulates user behavior in movie recommendation conversations.
"""

import base64
import json
import random
from openai import OpenAI

def encode_image(image_path):
    """Encode image to base64"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Image not found: {image_path}")
        return None

class MovieUser:
    def __init__(self, base_url: str, api_key: str):
        self.client = self.get_client(base_url, api_key)
        self.scenario = None
        self.requirement = None
        self.target_movie = None

    def get_client(self, base_url, api_key):
        """Initialize OpenAI client"""
        client = OpenAI(base_url=base_url, api_key=api_key)
        return client

    def load_user(self, scenario, requirement, target_movie):
        """Load user context"""
        self.scenario = scenario
        self.requirement = requirement
        self.target_movie = target_movie

    def clear_user(self):
        """Clear user context"""
        self.scenario = None
        self.requirement = None
        self.target_movie = None

    def movie_chit_chat(self, conversations, recommended_movie):
        """Generate chit-chat response about movies"""
        content_system = """You are roleplaying as a user in a movie recommendation conversation.
        The system just mentioned or recommended a movie to you.
        Respond with casual chit-chat rather than directly accepting or rejecting the recommendation.
        
        Your response should:
        1. Be related to movies, genres, or entertainment in general
        2. Draw from your personal movie-watching experiences
        3. Show interest in the conversation without committing to the recommendation
        4. Be natural and conversational (2-3 sentences)
        5. Reflect Indian movie-watching culture when appropriate
        
        Avoid directly saying yes/no to the recommendation."""
        
        movie_info = f"Movie mentioned: {recommended_movie.get('title', 'Unknown')}, Genres: {recommended_movie.get('categories', [])}"
        content_user = f"Conversations: {conversations}\nMovie info: {movie_info}\nYour background: {self.scenario}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.3,
        )
        
        return response.choices[0].message.content

    def accept_movie(self, conversations):
        """Generate acceptance response for movie recommendation"""
        content_system = """You are a user who has found a movie that perfectly matches your preferences.
        Express your satisfaction with the recommendation and explain why this movie appeals to you.
        
        Your response should:
        1. Show genuine enthusiasm for the recommended movie
        2. Mention specific aspects that appeal to you (genre, cast, story, etc.)
        3. Connect it to your original requirements or mood
        4. Thank the recommendation system
        5. Keep it natural and authentic (1-3 sentences)
        
        Be specific about what makes this movie a good choice for you."""
        
        movie_info = f"""
        Movie: {self.target_movie.get('title', 'Unknown')}
        Genres: {self.target_movie.get('categories', [])}
        Description: {self.target_movie.get('description', '')}
        Features: {self.target_movie.get('features', '')}
        """
        
        content_user = f"Conversations: {conversations}\nRecommended movie: {movie_info}\nYour context: {self.scenario}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.2,
        )
        
        return response.choices[0].message.content

    def find_movie_rejection_reasons(self, recommended_movie):
        """Find reasons to reject a movie recommendation"""
        content_system = """You are a user comparing a recommended movie with your ideal movie preference.
        Identify ONE main difference between the recommended movie and your target preference.
        
        Focus on differences in:
        1. Genre preferences
        2. Language or cultural context
        3. Mood or tone (serious vs light-hearted)
        4. Cast or director preferences
        5. Movie length or complexity
        
        Provide a brief, specific reason for declining based on the difference."""
        
        target_info = f"""
        Your ideal movie: {self.target_movie.get('title', 'Unknown')}
        Preferred genres: {self.target_movie.get('categories', [])}
        Your context: {self.scenario}
        Your requirements: {self.requirement}
        """
        
        recommended_info = f"""
        Recommended movie: {recommended_movie.get('title', 'Unknown')}
        Genres: {recommended_movie.get('categories', [])}
        Description: {recommended_movie.get('description', '')}
        """
        
        content_user = f"Your preferences: {target_info}\nRecommended: {recommended_info}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.2,
        )
        
        return response.choices[0].message.content

    def reject_movie(self, conversations, recommended_movie):
        """Generate rejection response for movie recommendation"""
        # First find the reason for rejection
        reject_reason = self.find_movie_rejection_reasons(recommended_movie)
        
        content_system = """You are a user politely declining a movie recommendation.
        You have a specific reason for not being interested in this movie.
        
        Your response should:
        1. Politely decline the recommendation
        2. Explain your reason clearly but kindly
        3. Provide hints about what you're actually looking for
        4. Keep the conversation flowing naturally
        5. Be constructive rather than just negative (1-3 sentences)
        
        Maintain a friendly tone while being clear about your preferences."""
        
        content_user = f"Conversations: {conversations}\nReason for declining: {reject_reason}\nYour context: {self.scenario}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.3,
        )
        
        return response.choices[0].message.content

    def express_movie_preferences(self, conversations):
        """Express initial movie preferences"""
        content_system = """You are a user starting a conversation with a movie recommendation system.
        Express your movie preferences and current mood/situation naturally.
        
        Your response should:
        1. Mention your current situation or mood
        2. Give hints about preferred genres or types of movies
        3. Mention any specific requirements (language, length, etc.)
        4. Be conversational and natural
        5. Reflect Indian movie-watching context when appropriate
        
        Don't be too specific initially - leave room for the system to ask questions."""
        
        content_user = f"Your scenario: {self.scenario}\nYour requirements: {self.requirement}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.3,
        )
        
        return response.choices[0].message.content

    def respond_to_clarification(self, conversations, clarification_question):
        """Respond to system's clarification questions"""
        content_system = """You are a user responding to a clarification question about your movie preferences.
        Provide helpful information while staying true to your target movie preference.
        
        Your response should:
        1. Answer the question helpfully
        2. Provide additional context about your preferences
        3. Give clues that lead toward your target movie
        4. Be natural and conversational
        5. Show engagement with the recommendation process
        
        Be specific enough to be helpful but not so specific that you reveal your exact target."""
        
        target_hints = f"""
        Your target movie: {self.target_movie.get('title', 'Unknown')}
        Target genres: {self.target_movie.get('categories', [])}
        Your context: {self.scenario}
        """
        
        content_user = f"Conversations: {conversations}\nQuestion: {clarification_question}\nYour preferences: {target_hints}"
        
        response = self.client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[
                {"role": "system", "content": content_system},
                {"role": "user", "content": content_user}
            ],
            temperature=0.3,
        )
        
        return response.choices[0].message.content
