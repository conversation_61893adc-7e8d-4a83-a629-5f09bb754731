[{"conversation_id": "clean_conv_001", "persona": {"name": "<PERSON><PERSON>", "age": 28, "region": "north", "language": "Hindi"}, "scenario": "Weekend movie night with family", "target_movie": "1001", "conversations": [{"turn": 1, "role": "Assistant", "message": "Hi! I'd love to help you find the perfect Bollywood movie. What's the occasion?", "action": "greeting", "media": null}, {"turn": 2, "role": "User", "message": "Hi! I'm planning a weekend movie night with family. Looking for something entertaining and feel-good.", "action": "express_preferences", "media": null}, {"turn": 3, "role": "Assistant", "message": "Perfect! I think you'd love 3 Idiots. It's a comedy-drama that's perfect for family viewing. Let me show you the poster:", "action": "recommend_with_poster", "media": {"type": "image", "poster_ref": "1001", "filename": null, "thumbnail": null, "alt_text": "3 Idiots movie poster showing the three main characters"}}, {"turn": 4, "role": "User", "message": "Perfect! The poster looks amazing and I've heard great things about this movie. This is exactly what we need!", "action": "accept", "media": null}], "metadata": {"total_turns": 4, "has_images": true, "image_count": 1, "conversation_type": "clean_multimodal"}}]