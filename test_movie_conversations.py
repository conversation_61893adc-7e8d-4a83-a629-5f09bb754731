#!/usr/bin/env python3
"""
Test script to generate sample movie recommendation conversations
without requiring API keys or vector databases.
"""

import json
import random
import os
from typing import Dict, List

class MockMovieRecommendationSystem:
    """Mock system for testing conversation generation"""
    
    def __init__(self, movies_data_path: str = "indian_movies_sample.json"):
        self.movies_data = self.load_movies_data(movies_data_path)
        self.conversation_templates = self.load_conversation_templates()
    
    def load_movies_data(self, path: str) -> Dict:
        """Load movies data"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Movies data file {path} not found.")
            return {}
    
    def load_conversation_templates(self) -> Dict:
        """Load conversation templates"""
        return {
            'system_greetings': [
                "Hi! I'm here to help you find the perfect movie to watch. What kind of mood are you in today?",
                "Hello! Looking for a good movie recommendation? Tell me what you're in the mood for!",
                "Hi there! I'd love to help you discover a great movie. What's the occasion?",
                "Welcome! Ready to find your next favorite movie? What are you looking for today?"
            ],
            'user_initial_responses': [
                "I'm looking for something entertaining to watch with my family.",
                "I want a good comedy movie for tonight.",
                "Can you suggest a drama that's not too heavy?",
                "I'm in the mood for an action movie with good story.",
                "Looking for a romantic movie for date night."
            ],
            'system_recommendations': [
                "Based on what you're looking for, I'd recommend {movie_title}. It's a {genres} that {reason}.",
                "I think you'd really enjoy {movie_title}. This {genres} movie {reason}.",
                "Perfect! {movie_title} sounds like exactly what you need. It's {reason}.",
                "How about {movie_title}? It's a wonderful {genres} that {reason}."
            ],
            'user_acceptances': [
                "That sounds perfect! I'll definitely watch {movie_title}.",
                "Great recommendation! {movie_title} is exactly what I was looking for.",
                "Perfect! I've heard good things about {movie_title}.",
                "Excellent choice! I'll add {movie_title} to my watchlist."
            ],
            'user_rejections': [
                "That's not quite what I'm looking for. Do you have something more {preference}?",
                "I'm not really in the mood for {genre}. Can you suggest something different?",
                "I've already seen {movie_title}. What else would you recommend?",
                "That sounds a bit too {characteristic} for me. Any other options?"
            ],
            'chit_chat_responses': [
                "I love movies with good storytelling and characters.",
                "I usually prefer movies that the whole family can enjoy.",
                "I'm really into {genre} movies lately.",
                "I like movies that are not too long, around 2 hours is perfect."
            ]
        }
    
    def generate_conversation(self, user_profile: Dict) -> Dict:
        """Generate a complete movie recommendation conversation"""
        
        profile = user_profile['profile']
        scenario = user_profile['scenario']
        target_movie = user_profile['target_item']
        
        conversation = []
        action_conv = []
        
        # System greeting
        greeting = random.choice(self.conversation_templates['system_greetings'])
        conversation.append({'Assistant': greeting})
        action_conv.append({
            'Assistant': greeting,
            'Action': 'greeting',
            'Mentioned_movie': [],
            'Image': []
        })
        
        # User initial response
        user_initial = self.generate_user_initial_response(scenario, user_profile['requirements'])
        conversation.append({'User': user_initial})
        action_conv.append({
            'User': user_initial,
            'Action': 'express_preferences',
            'Mentioned_movie': [],
            'Image': []
        })
        
        # Conversation rounds
        mentioned_movies = []
        for round_num in range(2, 5):  # 3 more rounds
            if round_num == 4:  # Final round - recommend target movie
                movie_to_recommend = target_movie
                accept = True
            else:
                # Recommend a different movie first
                movie_to_recommend = self.select_different_movie(target_movie, mentioned_movies)
                accept = False
            
            if movie_to_recommend:
                mentioned_movies.append(movie_to_recommend['item_id'])
                
                # System recommendation
                recommendation = self.generate_recommendation(movie_to_recommend, scenario)
                conversation.append({'Assistant': recommendation})
                action_conv.append({
                    'Assistant': recommendation,
                    'Action': 'recommend',
                    'Mentioned_movie': [movie_to_recommend['item_id']],
                    'Image': []
                })
                
                # User response
                if accept:
                    user_response = self.generate_acceptance(movie_to_recommend)
                    action = 'accept'
                else:
                    user_response = self.generate_rejection_or_chitchat(movie_to_recommend, target_movie)
                    action = 'reject' if 'not' in user_response.lower() else 'chit-chat'
                
                conversation.append({'User': user_response})
                action_conv.append({
                    'User': user_response,
                    'Action': action,
                    'Mentioned_movie': [],
                    'Image': []
                })
                
                if accept:
                    break
        
        return {
            'Persona': profile,
            'Scenario': scenario,
            'Target_movie': target_movie['item_id'],
            'Mentioned_movies': mentioned_movies,
            'Conversations': action_conv,
            'Simple_conversations': conversation
        }
    
    def generate_user_initial_response(self, scenario: str, requirements: str) -> str:
        """Generate user's initial response"""
        templates = [
            f"I'm {scenario.lower()} and {requirements.lower()}.",
            f"Hi! I'm {scenario.lower()}. {requirements}.",
            f"{requirements} I'm {scenario.lower()}.",
            f"Hello! {requirements} for {scenario.lower()}."
        ]
        return random.choice(templates)
    
    def select_different_movie(self, target_movie: Dict, mentioned_movies: List[str]) -> Dict:
        """Select a movie different from target for initial recommendations"""
        available_movies = []
        target_id = target_movie.get('item_id')
        
        for movie_id, movie_data in self.movies_data.items():
            if movie_id != target_id and movie_id not in mentioned_movies:
                available_movies.append(movie_data)
        
        return random.choice(available_movies) if available_movies else None
    
    def generate_recommendation(self, movie: Dict, scenario: str) -> str:
        """Generate system recommendation"""
        title = movie.get('title', 'Unknown')
        genres = ', '.join(movie.get('categories', []))
        
        reasons = [
            f"has great reviews and perfect for {scenario.lower()}",
            f"features excellent performances and fits your mood",
            f"is highly rated and exactly what you're looking for",
            f"has a compelling story that you'll really enjoy",
            f"is perfect for your current situation"
        ]
        
        reason = random.choice(reasons)
        template = random.choice(self.conversation_templates['system_recommendations'])
        
        return template.format(movie_title=title, genres=genres, reason=reason)
    
    def generate_acceptance(self, movie: Dict) -> str:
        """Generate user acceptance"""
        title = movie.get('title', 'Unknown')
        template = random.choice(self.conversation_templates['user_acceptances'])
        return template.format(movie_title=title)
    
    def generate_rejection_or_chitchat(self, recommended_movie: Dict, target_movie: Dict) -> str:
        """Generate user rejection or chit-chat"""
        if random.random() < 0.7:  # 70% rejection, 30% chit-chat
            return self.generate_rejection(recommended_movie, target_movie)
        else:
            return self.generate_chitchat(recommended_movie)
    
    def generate_rejection(self, recommended_movie: Dict, target_movie: Dict) -> str:
        """Generate user rejection"""
        recommended_genres = recommended_movie.get('categories', [])
        target_genres = target_movie.get('categories', [])
        
        if recommended_genres and target_genres:
            target_genre = random.choice(target_genres).lower()
            templates = [
                f"That's not quite what I'm looking for. Do you have something more {target_genre}?",
                f"I'm not really in the mood for {recommended_genres[0].lower()}. Can you suggest something different?",
                f"That sounds interesting, but I prefer {target_genre} movies. Any other options?"
            ]
        else:
            templates = [
                "That's not quite what I'm looking for. Do you have something different?",
                "I'm looking for something else. Can you suggest another movie?",
                "That doesn't match my mood right now. What else do you have?"
            ]
        
        return random.choice(templates)
    
    def generate_chitchat(self, movie: Dict) -> str:
        """Generate user chit-chat"""
        responses = [
            "I've heard good things about that movie. What else would you recommend?",
            "That's an interesting choice. I like movies with good storytelling.",
            "I usually prefer movies that are not too long. What's the runtime?",
            "That sounds good. I'm really into well-made movies lately."
        ]
        return random.choice(responses)

def main():
    """Generate sample movie conversations"""
    print("Generating sample movie recommendation conversations...")
    
    # Load user profiles
    try:
        with open('indian_user_profiles.json', 'r', encoding='utf-8') as f:
            user_profiles = json.load(f)
    except FileNotFoundError:
        print("User profiles not found. Please run generate_indian_user_profiles.py first.")
        return
    
    # Initialize mock system
    mock_system = MockMovieRecommendationSystem()
    
    # Create output directories
    os.makedirs('sample_movie_convs', exist_ok=True)
    
    # Generate conversations for first 5 profiles
    generated_conversations = []
    
    for i, profile in enumerate(user_profiles[:5]):
        try:
            conversation = mock_system.generate_conversation(profile)
            generated_conversations.append(conversation)
            
            # Save individual conversation
            with open(f'sample_movie_convs/conv_{i+1}.json', 'w', encoding='utf-8') as f:
                json.dump(conversation, f, ensure_ascii=False, indent=2)
            
            print(f"Generated conversation {i+1} for {profile['profile']['name']}")
            
        except Exception as e:
            print(f"Error generating conversation {i+1}: {e}")
            continue
    
    # Save all conversations
    with open('sample_movie_conversations.json', 'w', encoding='utf-8') as f:
        json.dump(generated_conversations, f, ensure_ascii=False, indent=2)
    
    print(f"\nGenerated {len(generated_conversations)} sample conversations!")
    
    # Print sample conversation
    if generated_conversations:
        print("\nSample conversation:")
        sample_conv = generated_conversations[0]['Simple_conversations']
        for turn in sample_conv:
            for role, message in turn.items():
                print(f"{role}: {message}")
        
        print(f"\nTarget movie: {generated_conversations[0]['Target_movie']}")
        print(f"Mentioned movies: {generated_conversations[0]['Mentioned_movies']}")

if __name__ == "__main__":
    main()
