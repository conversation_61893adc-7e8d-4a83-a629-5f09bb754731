[{"profile": {"name": "<PERSON><PERSON>", "age": 21, "gender": "female", "profession": "Artist", "region": "west", "state": "Maharashtra", "language": "Hindi"}, "scenario": "Want to watch something entertaining after work during sunday family time", "requirements": "Need something that's not too long and boring", "target_item": {"item_id": "1001", "title": "3 Idiots", "categories": ["Comedy", "Drama"], "description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots.", "new_description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Genres: Comedy, Drama. Released in 2009.", "price": "Rating: 8.4/10", "features": "Year: 2009, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Language: hi, Themes: Education, Friendship, Engineering, Bollywood"}}, {"profile": {"name": "<PERSON>", "age": 68, "gender": "female", "profession": "Artist", "region": "south", "state": "Andhra Pradesh", "language": "Malayalam"}, "scenario": "Planning to watch a movie with grandchildren", "requirements": "Looking for something with good story and acting", "target_item": {"item_id": "1005", "title": "<PERSON><PERSON><PERSON>", "categories": ["Adventure", "Drama", "Musical"], "description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers.", "new_description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Genres: Adventure, Drama, Musical. Released in 2001.", "price": "Rating: 8.1/10", "features": "Year: 2001, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Language: hi, Themes: Cricket, British Raj, Village Life, Patriotism"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 60, "gender": "female", "profession": "Homemaker", "region": "west", "state": "Maharashtra", "language": "Hindi"}, "scenario": "Want to watch something inspiring and uplifting", "requirements": "Want something that's not too modern or loud that's not too long", "target_item": {"item_id": "1002", "title": "Dangal", "categories": ["Biography", "Drama", "Sport"], "description": "Former wrestler <PERSON><PERSON><PERSON> and his two wrestler daughters struggle towards glory at the Commonwealth Games in the face of societal oppression.", "new_description": "Former wrestler <PERSON><PERSON><PERSON> and his two wrestler daughters struggle towards glory at the Commonwealth Games in the face of societal oppression. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Genres: Biography, Drama, Sport. Released in 2016.", "price": "Rating: 8.3/10", "features": "Year: 2016, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Language: hi, Themes: Wrestling, Women Empowerment, Sports, Family"}}, {"profile": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 27, "gender": "male", "profession": "Journalist", "region": "east", "state": "Odisha", "language": "Assamese"}, "scenario": "Searching for a movie to watch with college friends", "requirements": "Want a movie with good story and characters that's highly rated", "target_item": {"item_id": "1006", "title": "Queen", "categories": ["Comedy", "Drama"], "description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled.", "new_description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Genres: Comedy, Drama. Released in 2013.", "price": "Rating: 8.2/10", "features": "Year: 2013, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Language: hi, Themes: Women Empowerment, Solo Travel, Self Discovery"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 61, "gender": "female", "profession": "Sales Executive", "region": "west", "state": "Goa", "language": "Marathi"}, "scenario": "Planning to watch a movie with grandchildren", "requirements": "Want something that's not too modern or loud", "target_item": {"item_id": "1004", "title": "Zindagi Na Milegi <PERSON>", "categories": ["Adventure", "Comedy", "Drama"], "description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged.", "new_description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Adventure, Comedy, Drama. Released in 2011.", "price": "Rating: 8.2/10", "features": "Year: 2011, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Friendship, Travel, Adventure, Modern India"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 22, "gender": "female", "profession": "Consultant", "region": "west", "state": "Maharashtra", "language": "Gujarati"}, "scenario": "Need a good movie for a date night during festival holiday movie watching", "requirements": "Need something that's not too long and boring with famous actors", "target_item": {"item_id": "1004", "title": "Zindagi Na Milegi <PERSON>", "categories": ["Adventure", "Comedy", "Drama"], "description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged.", "new_description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Adventure, Comedy, Drama. Released in 2011.", "price": "Rating: 8.2/10", "features": "Year: 2011, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Friendship, Travel, Adventure, Modern India"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 60, "gender": "female", "profession": "Nurse", "region": "west", "state": "Maharashtra", "language": "Marathi"}, "scenario": "Want to watch something with good story and values", "requirements": "Looking for something with good story and acting with famous actors", "target_item": {"item_id": "1007", "title": "<PERSON><PERSON><PERSON>", "categories": ["Comedy", "Drama", "Family"], "description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life.", "new_description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Comedy, Drama, Family. Released in 2017.", "price": "Rating: 7.1/10", "features": "Year: 2017, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Middle Class, Radio, Mumbai, Family"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 18, "gender": "female", "profession": "Software Engineer", "region": "south", "state": "Karnataka", "language": "Tamil"}, "scenario": "Planning a movie night with friends", "requirements": "Need something that's not too long and boring with famous actors", "target_item": {"item_id": "1006", "title": "Queen", "categories": ["Comedy", "Drama"], "description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled.", "new_description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Genres: Comedy, Drama. Released in 2013.", "price": "Rating: 8.2/10", "features": "Year: 2013, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Language: hi, Themes: Women Empowerment, Solo Travel, Self Discovery"}}, {"profile": {"name": "<PERSON><PERSON><PERSON>", "age": 41, "gender": "male", "profession": "Chef", "region": "east", "state": "Odisha", "language": "Assamese"}, "scenario": "Planning a movie night for the family during monsoon evening entertainment", "requirements": "Looking for a good comedy movie", "target_item": {"item_id": "1007", "title": "<PERSON><PERSON><PERSON>", "categories": ["Comedy", "Drama", "Family"], "description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life.", "new_description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Comedy, Drama, Family. Released in 2017.", "price": "Rating: 7.1/10", "features": "Year: 2017, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Middle Class, Radio, Mumbai, Family"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 57, "gender": "female", "profession": "Chef", "region": "north", "state": "Punjab", "language": "Punjabi"}, "scenario": "Want to watch something inspiring and uplifting", "requirements": "Looking for something with good story and acting with good music", "target_item": {"item_id": "1005", "title": "<PERSON><PERSON><PERSON>", "categories": ["Adventure", "Drama", "Musical"], "description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers.", "new_description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Genres: Adventure, Drama, Musical. Released in 2001.", "price": "Rating: 8.1/10", "features": "Year: 2001, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Language: hi, Themes: Cricket, British Raj, Village Life, Patriotism"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 37, "gender": "female", "profession": "Artist", "region": "east", "state": "Odisha", "language": "Odia"}, "scenario": "Planning a movie night for the family", "requirements": "Need a movie with meaningful content", "target_item": {"item_id": "1005", "title": "<PERSON><PERSON><PERSON>", "categories": ["Adventure", "Drama", "Musical"], "description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers.", "new_description": "The people of a small village in Victorian India stake their future on a game of cricket against their ruthless British rulers. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Genres: Adventure, Drama, Musical. Released in 2001.", "price": "Rating: 8.1/10", "features": "Year: 2001, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Language: hi, Themes: Cricket, British Raj, Village Life, Patriotism"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 40, "gender": "male", "profession": "Chef", "region": "south", "state": "Andhra Pradesh", "language": "Tamil"}, "scenario": "Want to watch something with spouse after kids sleep during sunday family time", "requirements": "Need a movie with meaningful content", "target_item": {"item_id": "1001", "title": "3 Idiots", "categories": ["Comedy", "Drama"], "description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots.", "new_description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Genres: Comedy, Drama. Released in 2009.", "price": "Rating: 8.4/10", "features": "Year: 2009, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Language: hi, Themes: Education, Friendship, Engineering, Bollywood"}}, {"profile": {"name": "<PERSON><PERSON><PERSON>", "age": 20, "gender": "male", "profession": "Photographer", "region": "south", "state": "Tamil Nadu", "language": "Kannada"}, "scenario": "Looking for a good movie to watch on weekend during weekend movie time with family", "requirements": "Want a movie with good story and characters", "target_item": {"item_id": "1006", "title": "Queen", "categories": ["Comedy", "Drama"], "description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled.", "new_description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Genres: Comedy, Drama. Released in 2013.", "price": "Rating: 8.2/10", "features": "Year: 2013, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Language: hi, Themes: Women Empowerment, Solo Travel, Self Discovery"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 41, "gender": "female", "profession": "Journalist", "region": "south", "state": "Karnataka", "language": "Telugu"}, "scenario": "Planning a movie night for the family", "requirements": "Need a movie with meaningful content", "target_item": {"item_id": "1001", "title": "3 Idiots", "categories": ["Comedy", "Drama"], "description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots.", "new_description": "Two friends are searching for their long lost companion. They revisit their college days and recall the memories of their friend who inspired them to think differently, even as the rest of the world called them idiots. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Genres: Comedy, Drama. Released in 2009.", "price": "Rating: 8.4/10", "features": "Year: 2009, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Language: hi, Themes: Education, Friendship, Engineering, Bollywood"}}, {"profile": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "age": 55, "gender": "male", "profession": "Designer", "region": "east", "state": "West Bengal", "language": "Bengali"}, "scenario": "Want to watch something with good story and values", "requirements": "Want to watch a classic comedy movie with good music", "target_item": {"item_id": "1009", "title": "Super Deluxe", "categories": ["Comedy", "Drama", "Thriller"], "description": "An unfaithful newly-wed wife, an estranged father, a priest and an angry son suddenly find themselves in the most unexpected predicaments.", "new_description": "An unfaithful newly-wed wife, an estranged father, a priest and an angry son suddenly find themselves in the most unexpected predicaments. Directed by <PERSON><PERSON><PERSON><PERSON>. Starring <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Genres: Comedy, Drama, Thriller. Released in 2019.", "price": "Rating: 8.3/10", "features": "Year: 2019, Director: <PERSON><PERSON><PERSON><PERSON>, Cast: <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Language: ta, Themes: Tamil Cinema, Dark Comedy, Multiple Stories"}}, {"profile": {"name": "<PERSON><PERSON><PERSON><PERSON>", "age": 39, "gender": "female", "profession": "Nurse", "region": "west", "state": "Goa", "language": "Hindi"}, "scenario": "Want to watch something with spouse after kids sleep", "requirements": "Looking for a good comedy movie", "target_item": {"item_id": "1007", "title": "<PERSON><PERSON><PERSON>", "categories": ["Comedy", "Drama", "Family"], "description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life.", "new_description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Comedy, Drama, Family. Released in 2017.", "price": "Rating: 7.1/10", "features": "Year: 2017, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Middle Class, Radio, Mumbai, Family"}}, {"profile": {"name": "<PERSON><PERSON><PERSON><PERSON>", "age": 30, "gender": "male", "profession": "Lawyer", "region": "east", "state": "West Bengal", "language": "Assamese"}, "scenario": "Planning a movie night with friends during festival holiday movie watching", "requirements": "Want a movie with good story and characters", "target_item": {"item_id": "1007", "title": "<PERSON><PERSON><PERSON>", "categories": ["Comedy", "Drama", "Family"], "description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life.", "new_description": "A happy-go-lucky Mumbai suburban housewife <PERSON><PERSON><PERSON>, fondly known as <PERSON><PERSON>, lands the role of a night RJ, resulting in drastic changes to her routine life. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Comedy, Drama, Family. Released in 2017.", "price": "Rating: 7.1/10", "features": "Year: 2017, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Middle Class, Radio, Mumbai, Family"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 25, "gender": "female", "profession": "Chef", "region": "south", "state": "Tamil Nadu", "language": "Kannada"}, "scenario": "Need a good movie for a date night during weekend movie time with family", "requirements": "I want to watch a comedy movie", "target_item": {"item_id": "1004", "title": "Zindagi Na Milegi <PERSON>", "categories": ["Adventure", "Comedy", "Drama"], "description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged.", "new_description": "Three friends decide to turn their fantasy vacation into reality after one of their friends gets engaged. Directed by <PERSON><PERSON>. Starring <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Genres: Adventure, Comedy, Drama. Released in 2011.", "price": "Rating: 8.2/10", "features": "Year: 2011, Director: <PERSON><PERSON>, Cast: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Language: hi, Themes: Friendship, Travel, Adventure, Modern India"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 62, "gender": "female", "profession": "Consultant", "region": "north", "state": "Delhi", "language": "Hindi"}, "scenario": "Want to watch something with good story and values", "requirements": "Looking for something with good story and acting", "target_item": {"item_id": "1009", "title": "Super Deluxe", "categories": ["Comedy", "Drama", "Thriller"], "description": "An unfaithful newly-wed wife, an estranged father, a priest and an angry son suddenly find themselves in the most unexpected predicaments.", "new_description": "An unfaithful newly-wed wife, an estranged father, a priest and an angry son suddenly find themselves in the most unexpected predicaments. Directed by <PERSON><PERSON><PERSON><PERSON>. Starring <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Genres: Comedy, Drama, Thriller. Released in 2019.", "price": "Rating: 8.3/10", "features": "Year: 2019, Director: <PERSON><PERSON><PERSON><PERSON>, Cast: <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Language: ta, Themes: Tamil Cinema, Dark Comedy, Multiple Stories"}}, {"profile": {"name": "<PERSON><PERSON>", "age": 67, "gender": "male", "profession": "Business Owner", "region": "north", "state": "Delhi", "language": "Punjabi"}, "scenario": "Want to watch something with good story and values", "requirements": "Want to watch a classic drama movie", "target_item": {"item_id": "1006", "title": "Queen", "categories": ["Comedy", "Drama"], "description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled.", "new_description": "A Delhi girl from a traditional family sets out on a solo honeymoon after her marriage gets cancelled. Directed by <PERSON><PERSON><PERSON>. Starring <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Genres: Comedy, Drama. Released in 2013.", "price": "Rating: 8.2/10", "features": "Year: 2013, Director: <PERSON><PERSON><PERSON>, Cast: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Language: hi, Themes: Women Empowerment, Solo Travel, Self Discovery"}}]