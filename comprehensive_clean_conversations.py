#!/usr/bin/env python3
"""
Generate comprehensive clean multimodal conversations with local poster creation.
Follows 2025 MUSE framework with clean JSON structure (no base64 strings).
"""

import json
import os
import random
from typing import Dict, List, Optional
from PIL import Image, ImageDraw, ImageFont

class ComprehensiveCleanConversationGenerator:
    def __init__(self):
        self.poster_dir = "clean_movie_posters"
        self.thumbnail_dir = os.path.join(self.poster_dir, "thumbnails")
        
        # Create directories
        os.makedirs(self.poster_dir, exist_ok=True)
        os.makedirs(self.thumbnail_dir, exist_ok=True)
        
        # Enhanced Bollywood movie dataset
        self.bollywood_movies = {
            "1001": {
                "title": "3 Idiots", "year": 2009, "director": "<PERSON>",
                "cast": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
                "genres": ["Comedy", "Drama"], "language": "Hindi", "rating": 8.4,
                "description": "Two friends search for their lost companion while recalling college memories.",
                "color": "#FF6B6B"
            },
            "1002": {
                "title": "<PERSON>gal", "year": 2016, "director": "<PERSON><PERSON><PERSON>",
                "cast": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>ima <PERSON>a <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>hotra"],
                "genres": ["Biography", "Drama", "Sport"], "language": "Hindi", "rating": 8.3,
                "description": "A former wrestler trains his daughters to become world-class wrestlers.",
                "color": "#4ECDC4"
            },
            "1003": {
                "title": "Queen", "year": 2013, "director": "Vikas Bahl",
                "cast": ["Kangana Ranaut", "Rajkummar Rao", "Lisa Haydon"],
                "genres": ["Comedy", "Drama"], "language": "Hindi", "rating": 8.2,
                "description": "A Delhi girl goes on her honeymoon alone after her marriage is cancelled.",
                "color": "#96CEB4"
            },
            "1004": {
                "title": "Gully Boy", "year": 2019, "director": "Zoya Akhtar",
                "cast": ["Ranveer Singh", "Alia Bhatt", "Siddhant Chaturvedi"],
                "genres": ["Drama", "Music"], "language": "Hindi", "rating": 7.9,
                "description": "A coming-of-age story based on the lives of street rappers in Mumbai.",
                "color": "#85C1E9"
            },
            "1005": {
                "title": "Andhadhun", "year": 2018, "director": "Sriram Raghavan",
                "cast": ["Ayushmann Khurrana", "Tabu", "Radhika Apte"],
                "genres": ["Crime", "Mystery", "Thriller"], "language": "Hindi", "rating": 8.2,
                "description": "A blind pianist gets embroiled in a series of mysterious events.",
                "color": "#BB8FCE"
            }
        }
        
        # User profiles for different conversation types
        self.user_profiles = [
            {
                "name": "Priya Sharma", "age": 28, "gender": "female", "profession": "Software Engineer",
                "region": "north", "state": "Delhi", "language": "Hindi",
                "scenario": "Planning a movie night with friends during Diwali celebration",
                "requirements": "Looking for a feel-good comedy movie that everyone can enjoy"
            },
            {
                "name": "Arjun Reddy", "age": 35, "gender": "male", "profession": "Doctor",
                "region": "south", "state": "Andhra Pradesh", "language": "Telugu",
                "scenario": "Weekend family movie time with parents and children",
                "requirements": "Want an inspiring biographical movie with good values"
            },
            {
                "name": "Kavya Patel", "age": 24, "gender": "female", "profession": "Teacher",
                "region": "west", "state": "Gujarat", "language": "Gujarati",
                "scenario": "Solo movie watching after a stressful week at work",
                "requirements": "Need something empowering and uplifting for women"
            }
        ]
    
    def create_movie_poster(self, movie_id: str, movie_data: Dict) -> Dict:
        """Create a movie poster using PIL"""
        try:
            # Create poster image (500x750 standard movie poster ratio)
            img = Image.new('RGB', (500, 750), color=movie_data["color"])
            draw = ImageDraw.Draw(img)
            
            # Try to load fonts
            try:
                title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 36)
                info_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 24)
                small_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 18)
            except:
                title_font = ImageFont.load_default()
                info_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
            
            # Add title
            title = movie_data["title"]
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (500 - title_width) // 2
            
            # Draw title with outline
            for adj in range(-2, 3):
                for adj2 in range(-2, 3):
                    draw.text((title_x + adj, 50 + adj2), title, font=title_font, fill="black")
            draw.text((title_x, 50), title, font=title_font, fill="white")
            
            # Add year and director
            year_text = f"({movie_data['year']})"
            year_bbox = draw.textbbox((0, 0), year_text, font=info_font)
            year_width = year_bbox[2] - year_bbox[0]
            year_x = (500 - year_width) // 2
            draw.text((year_x, 120), year_text, font=info_font, fill="white")
            
            director_text = f"Directed by {movie_data['director']}"
            director_bbox = draw.textbbox((0, 0), director_text, font=small_font)
            director_width = director_bbox[2] - director_bbox[0]
            director_x = (500 - director_width) // 2
            draw.text((director_x, 160), director_text, font=small_font, fill="white")
            
            # Add cast
            cast_text = f"Starring: {', '.join(movie_data['cast'][:2])}"
            cast_bbox = draw.textbbox((0, 0), cast_text, font=small_font)
            cast_width = cast_bbox[2] - cast_bbox[0]
            cast_x = (500 - cast_width) // 2
            draw.text((cast_x, 200), cast_text, font=small_font, fill="white")
            
            # Add genres
            genres_text = " | ".join(movie_data["genres"])
            genres_bbox = draw.textbbox((0, 0), genres_text, font=small_font)
            genres_width = genres_bbox[2] - genres_bbox[0]
            genres_x = (500 - genres_width) // 2
            draw.text((genres_x, 240), genres_text, font=small_font, fill="white")
            
            # Add rating
            rating_text = f"★ {movie_data['rating']}/10"
            rating_bbox = draw.textbbox((0, 0), rating_text, font=info_font)
            rating_width = rating_bbox[2] - rating_bbox[0]
            rating_x = (500 - rating_width) // 2
            draw.text((rating_x, 680), rating_text, font=info_font, fill="gold")
            
            # Save poster
            poster_filename = f"{movie_id}_{movie_data['title'].replace(' ', '_')}.jpg"
            poster_path = os.path.join(self.poster_dir, poster_filename)
            img.save(poster_path, "JPEG", quality=90)
            
            # Create thumbnail
            img.thumbnail((150, 225), Image.Resampling.LANCZOS)
            thumbnail_filename = f"{movie_id}_thumb.jpg"
            thumbnail_path = os.path.join(self.thumbnail_dir, thumbnail_filename)
            img.save(thumbnail_path, "JPEG", quality=85)
            
            return {
                "filename": poster_filename,
                "path": poster_path,
                "thumbnail": thumbnail_filename,
                "thumbnail_path": thumbnail_path,
                "created": True
            }
            
        except Exception as e:
            print(f"Failed to create poster for {movie_data['title']}: {e}")
            return {"created": False}
    
    def create_all_posters(self) -> Dict:
        """Create posters for all movies"""
        print("🎨 Creating movie posters...")
        poster_metadata = {}
        
        for movie_id, movie_data in self.bollywood_movies.items():
            poster_info = self.create_movie_poster(movie_id, movie_data)
            if poster_info.get("created"):
                poster_metadata[movie_id] = poster_info
                print(f"✅ Created poster: {movie_data['title']}")
            else:
                print(f"❌ Failed: {movie_data['title']}")
        
        print(f"📊 Created {len(poster_metadata)}/{len(self.bollywood_movies)} posters")
        return poster_metadata
    
    def generate_greeting_conversation(self, profile: Dict, target_movie_id: str) -> Dict:
        """Generate conversation starting with greeting"""
        target_movie = self.bollywood_movies[target_movie_id]
        
        return {
            "conversation_id": f"greeting_conv_{random.randint(1000, 9999)}",
            "conversation_type": "greeting_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": "Hi there! I'm your Bollywood movie recommendation assistant. What brings you here today?",
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hi! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": f"Perfect! Based on what you're looking for, I think {target_movie['title']} would be ideal. Let me show you the poster:",
                    "action": "recommend_with_poster",
                    "phase": "recommendation",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} movie poster featuring {', '.join(target_movie['cast'][:2])}"
                    }
                },
                {
                    "turn": 4,
                    "role": "User",
                    "message": f"Wow! {target_movie['title']} looks perfect! The poster really captures what I'm looking for. This is exactly what I want to watch!",
                    "action": "accept",
                    "phase": "acceptance",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 4,
                "has_images": True,
                "image_count": 1,
                "phases": ["greeting", "preference_expression", "recommendation", "acceptance"]
            }
        }
    
    def generate_chitchat_conversation(self, profile: Dict, target_movie_id: str) -> Dict:
        """Generate conversation with chit-chat phase"""
        target_movie = self.bollywood_movies[target_movie_id]
        
        return {
            "conversation_id": f"chitchat_conv_{random.randint(1000, 9999)}",
            "conversation_type": "chitchat_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": "Hello! Ready to discover some amazing Bollywood cinema? Tell me about your mood!",
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hi! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": "That sounds like a perfect time for a good movie! Do you prefer recent releases or classic Bollywood? And what about your favorite genres?",
                    "action": "chit_chat",
                    "phase": "chit_chat",
                    "media": None
                },
                {
                    "turn": 4,
                    "role": "User",
                    "message": f"I really enjoy {', '.join(target_movie['genres']).lower()} movies, especially ones with good storytelling and strong characters. I prefer films that are both entertaining and meaningful.",
                    "action": "provide_details",
                    "phase": "clarification",
                    "media": None
                },
                {
                    "turn": 5,
                    "role": "Assistant",
                    "message": f"Excellent taste! I have the perfect recommendation for you - {target_movie['title']}. Here's the poster that shows exactly what makes this movie special:",
                    "action": "recommend_with_poster",
                    "phase": "recommendation",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} poster showcasing the {', '.join(target_movie['genres']).lower()} elements"
                    }
                },
                {
                    "turn": 6,
                    "role": "User",
                    "message": f"Perfect! {target_movie['title']} looks exactly like what I was hoping for. The poster really shows the quality and style I'm looking for!",
                    "action": "accept",
                    "phase": "acceptance",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 6,
                "has_images": True,
                "image_count": 1,
                "phases": ["greeting", "preference_expression", "chit_chat", "clarification", "recommendation", "acceptance"]
            }
        }
    
    def generate_comparison_conversation(self, profile: Dict, target_movie_id: str) -> Dict:
        """Generate conversation with movie comparison"""
        target_movie = self.bollywood_movies[target_movie_id]
        
        # Select a different movie for comparison
        other_movies = [mid for mid in self.bollywood_movies.keys() if mid != target_movie_id]
        comparison_movie_id = random.choice(other_movies)
        comparison_movie = self.bollywood_movies[comparison_movie_id]
        
        return {
            "conversation_id": f"comparison_conv_{random.randint(1000, 9999)}",
            "conversation_type": "comparison_based",
            "persona": profile,
            "scenario": profile["scenario"],
            "target_movie": target_movie_id,
            "conversations": [
                {
                    "turn": 1,
                    "role": "Assistant",
                    "message": "Welcome! I'd love to help you find your next favorite Bollywood film. What's the occasion?",
                    "action": "greeting",
                    "phase": "greeting",
                    "media": None
                },
                {
                    "turn": 2,
                    "role": "User",
                    "message": f"Hello! I'm {profile['scenario'].lower()}. {profile['requirements']}.",
                    "action": "express_preferences",
                    "phase": "preference_expression",
                    "media": None
                },
                {
                    "turn": 3,
                    "role": "Assistant",
                    "message": "Great! I have two excellent options for you. Let me show you both posters so you can see which one appeals to you more.",
                    "action": "show_options",
                    "phase": "option_presentation",
                    "media": None
                },
                {
                    "turn": 4,
                    "role": "Assistant",
                    "message": f"Option 1: {comparison_movie['title']} - Here's the poster:",
                    "action": "show_poster_option",
                    "phase": "option_1",
                    "media": {
                        "type": "image",
                        "poster_ref": comparison_movie_id,
                        "filename": f"{comparison_movie_id}_{comparison_movie['title'].replace(' ', '_')}.jpg",
                        "thumbnail": f"{comparison_movie_id}_thumb.jpg",
                        "alt_text": f"{comparison_movie['title']} poster"
                    }
                },
                {
                    "turn": 5,
                    "role": "Assistant",
                    "message": f"Option 2: {target_movie['title']} - And here's this poster:",
                    "action": "show_poster_option",
                    "phase": "option_2",
                    "media": {
                        "type": "image",
                        "poster_ref": target_movie_id,
                        "filename": f"{target_movie_id}_{target_movie['title'].replace(' ', '_')}.jpg",
                        "thumbnail": f"{target_movie_id}_thumb.jpg",
                        "alt_text": f"{target_movie['title']} poster"
                    }
                },
                {
                    "turn": 6,
                    "role": "User",
                    "message": f"I definitely prefer the second option! {target_movie['title']}'s poster really speaks to me. The visual style and everything about it looks perfect for what I want.",
                    "action": "choose_option",
                    "phase": "selection",
                    "media": None
                }
            ],
            "metadata": {
                "total_turns": 6,
                "has_images": True,
                "image_count": 2,
                "phases": ["greeting", "preference_expression", "option_presentation", "option_1", "option_2", "selection"]
            }
        }
    
    def generate_all_conversations(self) -> List[Dict]:
        """Generate all types of conversations"""
        conversations = []
        
        # Generate different conversation types
        conversation_types = [
            ("greeting", self.generate_greeting_conversation),
            ("chitchat", self.generate_chitchat_conversation),
            ("comparison", self.generate_comparison_conversation)
        ]
        
        movie_ids = list(self.bollywood_movies.keys())
        
        for i, (conv_type, generator_func) in enumerate(conversation_types):
            profile = self.user_profiles[i % len(self.user_profiles)]
            target_movie_id = movie_ids[i % len(movie_ids)]
            
            conversation = generator_func(profile, target_movie_id)
            conversations.append(conversation)
            
            print(f"✅ Generated {conv_type} conversation: {conversation['conversation_id']}")
        
        return conversations
    
    def save_clean_dataset(self, poster_metadata: Dict, conversations: List[Dict]):
        """Save all clean datasets"""
        
        # Create clean movie dataset
        clean_movies = {}
        for movie_id, movie_data in self.bollywood_movies.items():
            poster_info = poster_metadata.get(movie_id, {})
            
            clean_movies[movie_id] = {
                "item_id": movie_id,
                "title": movie_data["title"],
                "year": movie_data["year"],
                "director": movie_data["director"],
                "cast": movie_data["cast"],
                "categories": movie_data["genres"],
                "language": movie_data["language"],
                "rating": movie_data["rating"],
                "description": movie_data["description"],
                "poster": {
                    "available": poster_info.get("created", False),
                    "filename": poster_info.get("filename"),
                    "thumbnail": poster_info.get("thumbnail"),
                    "path": f"clean_movie_posters/{poster_info.get('filename', '')}" if poster_info.get("filename") else None
                }
            }
        
        # Save files
        with open('clean_bollywood_dataset_comprehensive.json', 'w', encoding='utf-8') as f:
            json.dump(clean_movies, f, ensure_ascii=False, indent=2)
        
        with open('comprehensive_clean_conversations.json', 'w', encoding='utf-8') as f:
            json.dump(conversations, f, ensure_ascii=False, indent=2)
        
        with open('poster_metadata_clean.json', 'w', encoding='utf-8') as f:
            json.dump(poster_metadata, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Saved comprehensive clean datasets:")
        print(f"   📊 clean_bollywood_dataset_comprehensive.json - {len(clean_movies)} movies")
        print(f"   🎭 comprehensive_clean_conversations.json - {len(conversations)} conversations")
        print(f"   📸 poster_metadata_clean.json - {len(poster_metadata)} poster records")

def main():
    """Generate comprehensive clean conversations"""
    print("🎬 Comprehensive Clean Multimodal Conversation Generator")
    print("Following 2025 MUSE Framework with Clean JSON Structure")
    print("=" * 70)
    
    generator = ComprehensiveCleanConversationGenerator()
    
    # Create movie posters
    poster_metadata = generator.create_all_posters()
    
    # Generate conversations
    print(f"\n🎭 Generating conversations...")
    conversations = generator.generate_all_conversations()
    
    # Save everything
    print(f"\n💾 Saving datasets...")
    generator.save_clean_dataset(poster_metadata, conversations)
    
    # Print summary
    total_turns = sum(conv['metadata']['total_turns'] for conv in conversations)
    total_images = sum(conv['metadata']['image_count'] for conv in conversations)
    
    print(f"\n🎉 Generation Complete!")
    print(f"✅ Generated {len(conversations)} comprehensive conversations")
    print(f"📝 Total conversation turns: {total_turns}")
    print(f"🖼️ Total images: {total_images}")
    print(f"🎨 Movie posters created: {len(poster_metadata)}")
    print(f"📁 All files use clean references (no base64 strings)")
    
    # Show conversation types
    print(f"\n📋 Conversation Types Generated:")
    for conv in conversations:
        print(f"   • {conv['conversation_type']}: {conv['metadata']['total_turns']} turns, {conv['metadata']['image_count']} images")
    
    return conversations

if __name__ == "__main__":
    main()
